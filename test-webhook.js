// 测试 webhook 通知功能
const axios = require('axios');

// 测试飞书通知
async function testFeishuWebhook() {
    const webhookUrl = 'https://open.feishu.cn/open-apis/bot/v2/hook/5c54881d-8441-4749-929f-3976683e5cca';
    
    const message = `
🧪 测试通知
--------------------
🌐 域名: localhost:3000
🤖 API 提供商: test
📱 模型: test-model
✅ 状态: 测试成功
⏱️ 响应时间: 1000ms
--------------------
📝 这是一条测试消息，用于验证 webhook 通知功能是否正常工作。
    `.trim();

    try {
        console.log('发送测试通知到飞书...');
        const response = await axios.post(webhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });
        
        console.log('✅ 飞书通知发送成功:', response.status);
        console.log('响应数据:', response.data);
    } catch (error) {
        console.error('❌ 飞书通知发送失败:', error.message);
        if (error.response) {
            console.error('错误状态:', error.response.status);
            console.error('错误数据:', error.response.data);
        }
    }
}

// 测试错误通知 webhook
async function testErrorWebhook() {
    const webhookUrl = 'https://open.feishu.cn/open-apis/bot/v2/hook/5f47ba52-5cb4-41bf-8652-3103ab362681';
    
    const message = `
🚨 AI 内容生成失败告警 (测试)
--------------------
🌐 域名: localhost:3000
🔧 API 提供商: vertex-ai
🤖 模型: gemini-2.0-flash
⚠️ 错误类型: test_error
📍 端点: /api/analysis/generate
--------------------
❌ 错误详情:
这是一条测试错误消息，用于验证错误通知功能。
--------------------
🔄 Vertex AI 失败，将尝试 OpenRouter 降级
    `.trim();

    try {
        console.log('发送测试错误通知到飞书...');
        const response = await axios.post(webhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });
        
        console.log('✅ 飞书错误通知发送成功:', response.status);
        console.log('响应数据:', response.data);
    } catch (error) {
        console.error('❌ 飞书错误通知发送失败:', error.message);
        if (error.response) {
            console.error('错误状态:', error.response.status);
            console.error('错误数据:', error.response.data);
        }
    }
}

async function main() {
    console.log('🚀 开始测试 Webhook 通知功能');
    console.log('================================');
    
    console.log('\n1. 测试付费 API 调用通知...');
    await testFeishuWebhook();
    
    console.log('\n2. 测试 AI 生成错误通知...');
    await testErrorWebhook();
    
    console.log('\n✅ 测试完成！');
}

main().catch(console.error);
