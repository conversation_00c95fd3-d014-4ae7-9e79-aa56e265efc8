import { Link } from '@/i18n/routing'
import { TestType } from '@prisma/client'

interface TestCardProps {
    type: TestType
}

export default function TestCard({ type }: TestCardProps) {
    const isRAADS = type === TestType.RAADS

    return (
        <Link
            href={isRAADS ? "/raads-r-test" : "/adhd-test" as any}
            className="block p-4 rounded-lg border border-primary/10 hover:border-primary/20 transition-colors"
        >
            <h3 className="text-lg font-semibold text-primary mb-2">
                {isRAADS ? 'RAADS-R Test' : 'ADHD Test'}
            </h3>
            <p className="text-sm text-gray-600">
                {isRAADS
                    ? 'Assess autism spectrum traits with the RAADS-R test'
                    : 'Evaluate ADHD symptoms with our comprehensive assessment'}
            </p>
        </Link>
    )
} 