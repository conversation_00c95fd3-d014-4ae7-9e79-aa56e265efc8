'use client'

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useTracking } from '@/hooks/useTracking';

interface Question {
    id: number;
    text: string;
    options: string[];
}

// Add scoring constants
const scoreMap = {
    "symptom": [3, 2, 1, 0],
    "non-symptom": [0, 1, 2, 3]
};

// Add non-symptom questions (questions that are scored in reverse)
const nonSymptomQuestions = [1, 6, 11, 18, 23, 26, 33, 37, 43, 47, 53, 58, 62, 68, 72, 77];

// Add dimension mappings
const dimensionQuestions = {
    socialRelatedness: [1, 6, 8, 11, 14, 17, 18, 25, 37, 38, 3, 5, 12, 28, 39, 44, 45, 76, 79, 80, 20, 21, 22, 23, 26, 31, 43, 47, 48, 53, 54, 55, 60, 61, 64, 68, 69, 72, 77],
    circumscribedInterests: [9, 13, 24, 30, 32, 40, 41, 50, 52, 56, 63, 70, 75, 78],
    language: [2, 7, 27, 35, 58, 66, 15],
    sensoryMotor: [10, 19, 4, 33, 34, 36, 46, 71, 16, 29, 42, 49, 51, 57, 59, 62, 65, 67, 73, 74]
};

export default function RAADSTest() {
    const t = useTranslations('components.test');
    const [currentQuestion, setCurrentQuestion] = useState(0);
    const [answers, setAnswers] = useState<Record<number, number>>({});
    const [email, setEmail] = useState('');
    const [isComplete, setIsComplete] = useState(false);
    const [showError, setShowError] = useState(false);
    const questionRef = useRef<HTMLDivElement>(null);
    const router = useRouter();
    const { trackAction } = useTracking();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);

    const questions: Question[] = Array.from({ length: 80 }, (_, i) => {
        const questionNumber = i + 1;
        try {
            return {
                id: questionNumber,
                text: t(`questions.${questionNumber}`),
                options: [
                    t('options.neverTrue'),
                    t('options.trueOnlyBefore'),
                    t('options.trueSometimes'),
                    t('options.trueNow'),
                ]
            };
        } catch (error) {
            console.error(`Failed to load question ${questionNumber}:`, error);
            return {
                id: questionNumber,
                text: `Question ${questionNumber} (translation missing)`,
                options: [
                    'Never true',
                    'True now only',
                    'True only when I was younger than 16',
                    'True now and when I was young'
                ]
            };
        }
    });

    const calculateScore = () => {
        let totalScore = 0;
        let scores = {
            socialRelatedness: 0,
            circumscribedInterests: 0,
            language: 0,
            sensoryMotor: 0
        };

        Object.entries(answers).forEach(([index, answer]) => {
            const question = questions[parseInt(index)];

            // Calculate total score
            if (nonSymptomQuestions.includes(question.id)) {
                totalScore += scoreMap["non-symptom"][answer];
            } else {
                totalScore += scoreMap["symptom"][answer];
            }

            // Calculate dimension scores
            Object.entries(dimensionQuestions).forEach(([dimension, questionIds]) => {
                if (questionIds.includes(question.id)) {
                    if (nonSymptomQuestions.includes(question.id)) {
                        scores[dimension as keyof typeof scores] += scoreMap["non-symptom"][answer];
                    } else {
                        scores[dimension as keyof typeof scores] += scoreMap["symptom"][answer];
                    }
                }
            });
        });

        return { totalScore, scores };
    };

    const progressPercentage = (Object.keys(answers).length / questions.length) * 100;
    const isCurrentQuestionAnswered = answers.hasOwnProperty(currentQuestion);

    // Add scroll handling
    const scrollToQuestion = () => {
        if (questionRef.current) {
            const questionElement = questionRef.current;
            const elementRect = questionElement.getBoundingClientRect();
            const absoluteElementTop = elementRect.top + window.pageYOffset;
            const middle = absoluteElementTop - (window.innerHeight / 2) + (elementRect.height / 2);

            window.scrollTo({
                top: middle,
                behavior: 'smooth'
            });
        }
    };

    // Scroll when question changes
    useEffect(() => {
        scrollToQuestion();
    }, [currentQuestion]);

    const handleNavigation = (direction: 'prev' | 'next') => {
        if (direction === 'prev' && currentQuestion > 0) {
            setCurrentQuestion(prev => prev - 1);
            setShowError(false);
        } else if (direction === 'next') {
            if (!isCurrentQuestionAnswered) {
                setShowError(true);
                return;
            }
            if (currentQuestion < questions.length - 1) {
                setCurrentQuestion(prev => prev + 1);
                setShowError(false);
            }
        }
    };

    const handleAnswer = (questionId: number, answerValue: number) => {
        setAnswers(prev => ({
            ...prev,
            [currentQuestion]: answerValue
        }));
        setShowError(false);

        // Remove auto-navigation to next question
        // Only check if test is complete
        if (Object.keys(answers).length === questions.length - 1) {
            setIsComplete(true);
        }
    };

    const handleSubmitEmail = async () => {
        if (!email || isSubmitting) return;

        setIsSubmitting(true);
        setSubmitError(null);

        const finalScores = calculateScore();

        try {
            const response = await fetch('/api/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'RAADS',
                    email: email,
                    scores: finalScores,
                    answers: answers
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save test results');
            }

            const data = await response.json();

            if (!data.test?.id) {
                throw new Error('Invalid response: missing test ID');
            }

            // 使用返回的 test.id 进行跳转
            router.push(`/raads-r-test/${data.test.id}/analysis`);
        } catch (error) {
            console.error('Error saving test results:', error);
            setSubmitError(t('completion.errorSubmitting'));
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleStartTest = () => {
        trackAction('start_test', 'test', 'start');
        // ... rest of your code
    };

    const handleSubmitAnswer = (questionId: number, answer: string) => {
        trackAction('answer_question', 'test', `q${questionId}`, 1, { answer });
        // ... rest of your code
    };

    return (
        <div className="space-y-8">
            {/* Main Test Container */}
            <div className="bg-bg-100 rounded-lg shadow-lg p-4 sm:p-6">
                {!isComplete ? (
                    <motion.div
                        ref={questionRef}
                        key={currentQuestion}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        className="space-y-4 sm:space-y-6"
                    >
                        <div className="mb-6 sm:mb-8">
                            {/* Question Number */}
                            <div className="text-sm text-primary-100 mb-2 font-medium">
                                {t('navigation.questionProgress', {
                                    current: currentQuestion + 1,
                                    total: questions.length
                                })}
                            </div>

                            {/* Question Text */}
                            <h3 className="text-lg sm:text-xl font-semibold text-text-100 mb-4 leading-relaxed">
                                {questions[currentQuestion].text}
                            </h3>

                            {/* Options */}
                            <div className="space-y-2 sm:space-y-3">
                                {questions[currentQuestion].options.map((option, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleAnswer(questions[currentQuestion].id, index)}
                                        className={`w-full text-left p-3 sm:p-4 rounded-lg border transition-colors
                                            ${answers[currentQuestion] === index
                                                ? 'border-primary-100 bg-primary-300/20 text-primary-100'
                                                : 'border-bg-300 hover:bg-primary-300/10 hover:border-primary-200'
                                            }
                                            text-base sm:text-lg leading-relaxed`}
                                    >
                                        {option}
                                    </button>
                                ))}
                            </div>

                            {/* Error Message */}
                            {showError && (
                                <motion.div
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="mt-3 text-accent-200 text-sm px-1"
                                >
                                    {t('navigation.pleaseSelectAnOption')}
                                </motion.div>
                            )}
                        </div>

                        {/* Progress and Navigation */}
                        <div className="space-y-4 mt-auto">
                            {/* Progress Bar */}
                            <div className="flex items-center gap-3">
                                <div className="flex-1 h-2 bg-bg-200 rounded-full overflow-hidden">
                                    <div
                                        className="h-full bg-primary-100 transition-all duration-300"
                                        style={{ width: `${progressPercentage}%` }}
                                    />
                                </div>
                                <div className="text-sm font-medium text-text-200 min-w-[3rem] text-right">
                                    {t('navigation.progressPercentage', {
                                        percent: Math.round(progressPercentage)
                                    })}
                                </div>
                            </div>

                            {/* Navigation Buttons */}
                            <div className="flex justify-between mt-6">
                                {currentQuestion > 0 && (
                                    <button
                                        onClick={() => handleNavigation('prev')}
                                        className="flex-1 py-3 px-6 text-text-200 bg-bg-100 rounded-lg 
                                                 hover:bg-bg-200 transition-colors duration-200 mr-3
                                                 text-base font-medium border border-bg-300"
                                    >
                                        {t('navigation.previous')}
                                    </button>
                                )}

                                {currentQuestion < questions.length - 1 && (
                                    <button
                                        onClick={() => handleNavigation('next')}
                                        className="flex-1 py-3 px-6 bg-primary-100 text-bg-100 rounded-lg 
                                                 hover:bg-primary-200 transition-colors duration-200
                                                 text-base font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled={!isCurrentQuestionAnswered}
                                    >
                                        {t('navigation.next')}
                                    </button>
                                )}
                            </div>
                        </div>
                    </motion.div>
                ) : (
                    // Completion Section
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="space-y-4 sm:space-y-6 p-2"
                    >
                        <h3 className="text-xl sm:text-2xl font-semibold mb-4 text-text-100">
                            {t('completion.title')}
                        </h3>
                        <p className="text-text-200 text-base sm:text-lg leading-relaxed">
                            {t('completion.emailPrompt')}
                        </p>
                        <input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder={t('completion.emailPlaceholder')}
                            disabled={isSubmitting}
                            className="w-full p-3 sm:p-4 border border-bg-300 rounded-lg 
                                text-base sm:text-lg focus:outline-none focus:border-primary-100
                                text-text-100 placeholder:text-text-200
                                disabled:bg-bg-200 disabled:cursor-not-allowed"
                        />
                        {submitError && (
                            <p className="text-red-500 text-sm">{submitError}</p>
                        )}
                        <button
                            onClick={handleSubmitEmail}
                            disabled={isSubmitting}
                            className="w-full p-3 sm:p-4 bg-primary-100 text-bg-100 rounded-lg 
                                hover:bg-primary-200 text-base sm:text-lg font-medium
                                transition-colors duration-200
                                disabled:opacity-50 disabled:cursor-not-allowed
                                flex items-center justify-center"
                        >
                            {isSubmitting ? (
                                <>
                                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-bg-100 border-t-transparent mr-2" />
                                    {t('completion.submitting')}
                                </>
                            ) : (
                                t('completion.getReport')
                            )}
                        </button>
                    </motion.div>
                )}
            </div>

            {/* Disclaimer Section - Updated styles */}
            <div className="px-1 sm:px-2">
                <p className="text-text-200/80 text-xs sm:text-sm leading-relaxed text-center max-w-2xl mx-auto">
                    {t('disclaimer')}
                </p>
            </div>
        </div>
    );
} 