'use client'

import { Suspense, useEffect, useRef } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { notifyFeishu } from '@/helpers/notifyFeishu';
import { getIpInfo } from '@/helpers/ipTracker';
import { logEvent } from '@/helpers/gaLog';

function PageViewTrackerInner() {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const lastPathname = useRef<string>();

    useEffect(() => {
        if (lastPathname.current === pathname) return;
        lastPathname.current = pathname;

        const trackPageView = async () => {
            try {
                // Track in GA (non-blocking)
                Promise.resolve().then(() => {
                    logEvent('page_view', 'navigation', pathname, 1);
                });

                // Track in Feishu (non-blocking)
                Promise.resolve().then(async () => {
                    const ipInfo = await getIpInfo();
                    const timestamp = new Date().toISOString();
                    const referrer = document?.referrer || 'Direct';
                    const userAgent = navigator.userAgent;
                    const screenSize = `${window.innerWidth}x${window.innerHeight}`;
                    const query = searchParams.toString();

                    const message = `
                        📍 Page View
                        Time: ${timestamp}
                        Path: ${pathname}${query ? `?${query}` : ''}
                        IP: ${ipInfo?.ip || 'Unknown'}
                        Location: ${ipInfo?.city || 'Unknown'}, ${ipInfo?.region || ''}, ${ipInfo?.country || ''}
                        Timezone: ${ipInfo?.timezone || 'Unknown'}
                        Referrer: ${referrer}
                        Device: ${userAgent}
                        Screen: ${screenSize}
                    `.trim();

                    await notifyFeishu.notify(message);
                });
            } catch (error) {
                console.error('Failed to track page view:', error);
            }
        };

        trackPageView();
    }, [pathname, searchParams]);

    return null;
}

export default function PageViewTracker() {
    return (
        <Suspense fallback={null}>
            <PageViewTrackerInner />
        </Suspense>
    );
} 