'use client'

import { useTranslations } from 'next-intl';

export default function ADHDScoreInterpretation() {
    const t = useTranslations('components.scoreInterpretation.adhd');

    const getColorClass = (color: string) => {
        const colorMap: { [key: string]: string } = {
            green: "border-green-500",
            blue: "border-blue-500",
            yellow: "border-yellow-500",
            orange: "border-orange-500",
            red: "border-red-500",
            purple: "border-purple-500",
            gray: "border-gray-500"
        };
        return colorMap[color] || "border-gray-500";
    };

    return (
        <div className="space-y-6">
            <div className="flex flex-col gap-2">
                <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-primary">
                    {t('title')}
                </h2>
                <p className="text-primary/60 text-sm sm:text-base">
                    {t('subtitle')}
                </p>
            </div>

            <div className="grid gap-4 sm:gap-6">
                {t.raw('scoreList').map((score: any, index: number) => (
                    <div
                        key={index}
                        className={`bg-primary/[0.02] rounded-lg p-4 sm:p-5 hover:bg-primary/[0.04] 
                                  transition-colors duration-200 border-l-4 ${getColorClass(score.color)}`}
                    >
                        <h3 className="font-medium text-base sm:text-lg text-primary mb-2">
                            {score.range}
                        </h3>
                        <p className="text-primary/70 text-sm sm:text-base leading-relaxed">
                            {score.description}
                        </p>
                    </div>
                ))}
            </div>

            <div className="mt-4 text-sm text-gray-500">
                <p>{t('disclaimer')}</p>
            </div>
        </div>
    );
} 