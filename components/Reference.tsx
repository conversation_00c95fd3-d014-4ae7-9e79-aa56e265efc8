'use client'

import { FaLink } from 'react-icons/fa6'

interface ReferenceProps {
    citation: string
    link: string
}

export default function Reference({ citation, link }: ReferenceProps) {
    return (
        <section className="mt-8 pt-6 border-t border-bg-300/10">
            <div className="space-y-4">
                <h3 className="text-lg font-semibold text-text-100">Reference</h3>
                <div className="flex items-start gap-3 bg-bg-200 rounded-lg p-4">
                    <div className="flex-1">
                        <p className="text-sm text-text-200 leading-relaxed">
                            {citation}
                        </p>
                    </div>
                    <a
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-shrink-0 text-primary-100 hover:text-primary-200 transition-colors"
                        aria-label="View source"
                    >
                        <FaLink className="w-5 h-5" />
                    </a>
                </div>
            </div>
        </section>
    )
} 