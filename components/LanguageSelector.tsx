'use client'

import { useState, useRef, useEffect } from 'react';
import { useRouter, usePathname, locales, fullLanguageNames } from '@/i18n/routing';
import { useLocale } from 'next-intl';

// 添加这个类型声明
type Locale = keyof typeof fullLanguageNames;

const flagEmojis: Record<string, string> = {
    'en': '🇺🇸',
    'zh': '🇨🇳',
    'tw': '🇨🇳',
    'de': '🇩🇪',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'fr': '🇫🇷',
    'pt': '🇵🇹',
    'es': '🇪🇸',
    'vi': '🇻🇳',
    'ar': '🇸🇦',
    'nl': '🇳🇱',
    'pl': '🇵🇱',
    // Add more languages as needed
};

interface LanguageSelectorProps {
    onLanguageChange?: (lang: string) => void;
}

export default function LanguageSelector({ onLanguageChange }: LanguageSelectorProps) {
    const [isOpen, setIsOpen] = useState(false);
    // 明确指定 currentLocale 的类型
    const currentLocale = useLocale() as Locale;
    const pathname = usePathname();
    const router = useRouter();
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleLanguageChange = async (newLocale: string) => {
        onLanguageChange?.(newLocale);
        try {
            await router.replace(pathname as any, { locale: newLocale });
            router.refresh();
            setIsOpen(false);
        } catch (error) {
            console.error('Error changing language:', error);
        }
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="p-1 sm:p-2 rounded-md hover:bg-gray-100 transition-colors focus:outline-none flex items-center justify-center"
                aria-label="Select language"
            >
                <span className="text-lg sm:text-xl">{flagEmojis[currentLocale]}</span>
                <span className="hidden md:inline ml-2 text-sm font-medium">{fullLanguageNames[currentLocale]}</span>
                <svg className="hidden md:inline w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
            </button>
            {isOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    {locales.map((locale) => (
                        <button
                            key={locale}
                            onClick={() => handleLanguageChange(locale)}
                            className={`flex items-center space-x-2 w-full text-left px-4 py-2 text-sm ${currentLocale === locale ? 'text-[#5C9EDA]' : 'text-[#4A4A4A] hover:bg-[#F7F7F7]'
                                }`}
                        >
                            <span className="text-xl">{flagEmojis[locale]}</span>
                            <span className="text-sm">{fullLanguageNames[locale]}</span>
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
}