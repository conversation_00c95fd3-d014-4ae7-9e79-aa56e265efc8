'use client'

import { Link } from '@/i18n/routing'
import Image from "next/image";
import LanguageSelector from '@/components/LanguageSelector';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AnalysisButton from '@/components/AnalysisButton';

export default function Header() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        // Initial reveal animation
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 500);

        return () => {
            clearTimeout(timer);
        };
    }, []);

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.header
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                        duration: 0.5,
                        ease: "easeOut"
                    }}
                    className="fixed top-0 left-0 right-0 z-50 backdrop-blur-lg bg-white/70 shadow-sm border-b border-primary/10"
                >
                    <div className="container mx-auto px-4 py-3 sm:py-4">
                        <nav className="flex flex-wrap justify-between items-center gap-2 sm:gap-4">
                            <Link href="/" className="flex items-center space-x-2 sm:space-x-3">
                                <div className="relative w-8 h-8 sm:w-10 sm:h-10">
                                    <Image
                                        src="/logo.png"
                                        alt="RAADS-R Test"
                                        fill
                                        className="object-contain"
                                        priority
                                    />
                                </div>
                                <div className="flex flex-col">
                                    <div className="flex items-baseline">
                                        <span className="text-xl sm:text-2xl font-bold text-neutral-900 tracking-wide whitespace-nowrap font-chakra">
                                            AutismTestFree
                                        </span>
                                        <span className="text-sm sm:text-base font-light text-neutral-900 tracking-wide pl-[2px]">
                                            .com
                                        </span>
                                    </div>
                                    <span className="text-[10px] sm:text-xs text-emerald-600 tracking-wider uppercase font-light">
                                        Expert Psychological Tests
                                    </span>
                                </div>
                            </Link>
                            <div className="flex items-center space-x-3 sm:space-x-4">
                                <a
                                    href="https://knowledge.autismtestfree.com"
                                    className="p-1 sm:p-2 rounded-md hover:bg-gray-100 transition-colors focus:outline-none flex items-center justify-center"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <span className="text-lg sm:text-xl">📚</span>
                                    <span className="hidden md:inline ml-2 text-sm font-medium">Knowledge Center</span>
                                </a>
                                <LanguageSelector />
                                <AnalysisButton />
                            </div>
                        </nav>
                    </div>
                </motion.header>
            )}
        </AnimatePresence>
    );
}