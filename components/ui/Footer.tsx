import { useTranslations } from "next-intl";
import { Link } from '@/i18n/routing';
import { fullLanguageNames, locales } from '@/i18n/routing';

export default function Footer() {
    const t = useTranslations('components.footer');

    return (
        <footer className="bg-[#f5f5f5] border-t border-primary/5 mt-8">
            <div className="container mx-auto px-4 py-6 sm:py-8">
                <div className="text-center mb-4">
                    <h3 className="text-xs sm:text-sm font-medium text-[#5c5c5c] mb-2">
                        {t('precaution.title')}
                    </h3>
                    <p className="text-xs sm:text-sm text-[#5c5c5c]/80 max-w-2xl mx-auto leading-relaxed">
                        {t('precaution.description')}
                    </p>
                </div>
                <div className="flex flex-wrap justify-center space-x-4 sm:space-x-6 text-xs sm:text-sm text-[#5c5c5c]/80 mb-4">
                    <Link href="/privacy-policy" className="hover:text-primary-100 transition-colors">
                        {t('links.privacyPolicy')}
                    </Link>
                    <Link href="/terms-of-service" className="hover:text-primary-100 transition-colors">
                        {t('links.termsOfService')}
                    </Link>
                    <Link href="/disclaimer" className="hover:text-primary-100 transition-colors">
                        {t('links.disclaimer')}
                    </Link>
                    <Link href="/refund-policy" className="hover:text-primary-100 transition-colors">
                        {t('links.refundPolicy')}
                    </Link>
                </div>
                <div className="flex flex-wrap justify-center gap-x-4 gap-y-2 text-xs text-[#5c5c5c]/60">
                    {locales.map((locale) => (
                        <Link
                            key={locale}
                            href="/"
                            locale={locale}
                            className="hover:text-primary-100 transition-colors whitespace-nowrap"
                        >
                            {fullLanguageNames[locale]}
                        </Link>
                    ))}
                </div>
            </div>
        </footer>
    )
}