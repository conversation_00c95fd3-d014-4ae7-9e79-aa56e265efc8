import { useTranslations } from 'next-intl';

export default function FAQSection() {
    const t = useTranslations('components.faq');

    return (
        <div className="space-y-6">
            <div className="flex flex-col gap-2">
                <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-primary">
                    {t('title')}
                </h2>
                <p className="text-primary/60 text-sm sm:text-base">
                    {t('subtitle')}
                </p>
            </div>

            <div className="grid gap-4 sm:gap-6">
                {t.raw('items').map((faq: any, index: number) => (
                    <div
                        key={index}
                        className="bg-primary/[0.02] rounded-lg p-4 sm:p-5 hover:bg-primary/[0.04] transition-colors duration-200"
                    >
                        <h3 className="font-medium text-base sm:text-lg text-primary mb-2">
                            {faq.question}
                        </h3>
                        <p className="text-primary/70 text-sm sm:text-base leading-relaxed">
                            {faq.answer}
                        </p>
                    </div>
                ))}
            </div>
        </div>
    );
} 