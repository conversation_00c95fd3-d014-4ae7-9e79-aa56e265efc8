'use client'

import { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useTracking } from '@/hooks/useTracking'

interface Question {
    id: number
    text: string
    options: string[]
}

// Define scoring dimensions based on VADRS
const dimensionQuestions = {
    inattention: Array.from({ length: 9 }, (_, i) => i + 1),
    hyperactivityImpulsivity: Array.from({ length: 9 }, (_, i) => i + 10),
    oppositionalDefiant: Array.from({ length: 8 }, (_, i) => i + 19),
    conductDisorder: Array.from({ length: 15 }, (_, i) => i + 27),
    anxiety: Array.from({ length: 3 }, (_, i) => i + 42),
    performance: Array.from({ length: 8 }, (_, i) => i + 48)
}

export default function ADHDTest() {
    const t = useTranslations('components.adhd-test')
    const [currentQuestion, setCurrentQuestion] = useState(0)
    const [answers, setAnswers] = useState<Record<number, number>>({})
    const [email, setEmail] = useState('')
    const [isComplete, setIsComplete] = useState(false)
    const [showError, setShowError] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [submitError, setSubmitError] = useState<string | null>(null)
    const questionRef = useRef<HTMLDivElement>(null)
    const router = useRouter()
    const { trackAction } = useTracking()

    // Generate questions array from translations
    const questions: Question[] = Array.from({ length: 55 }, (_, i) => {
        const questionNumber = i + 1
        try {
            return {
                id: questionNumber,
                text: t(`questions.${questionNumber}`),
                options: questionNumber <= 47 ? [
                    t('options.never'),
                    t('options.occasionally'),
                    t('options.often'),
                    t('options.veryOften')
                ] : [
                    t('options.excellent'),
                    t('options.aboveAverage'),
                    t('options.average'),
                    t('options.somewhatProblematic'),
                    t('options.problematic')
                ]
            }
        } catch (error) {
            console.error(`Failed to load question ${questionNumber}:`, error)
            return {
                id: questionNumber,
                text: `Question ${questionNumber} (translation missing)`,
                options: questionNumber <= 47 ?
                    ['Never', 'Occasionally', 'Often', 'Very Often'] :
                    ['Excellent', 'Above Average', 'Average', 'Somewhat of a Problem', 'Problematic']
            }
        }
    })

    const calculateScore = () => {
        // Count responses of 2 or 3 for behavioral questions
        const countSignificantResponses = (questions: number[]) => {
            return questions.filter(id => answers[id - 1] >= 2).length;
        };

        // Count responses of 4 or 5 for performance questions
        const countPerformanceIssues = (questions: number[]) => {
            return questions.filter(id => answers[id - 1] >= 3).length;
        };

        const inattentiveScore = countSignificantResponses(dimensionQuestions.inattention);
        const hyperactiveScore = countSignificantResponses(dimensionQuestions.hyperactivityImpulsivity);
        const performanceIssues = countPerformanceIssues(dimensionQuestions.performance);

        // Determine ADHD subtype based on scores
        let subtype: 'inattentive' | 'hyperactive' | 'combined' | 'none' = 'none';

        const hasInattentive = inattentiveScore >= 6;
        const hasHyperactive = hyperactiveScore >= 6;

        if (hasInattentive && hasHyperactive) {
            subtype = 'combined';
        } else if (hasInattentive) {
            subtype = 'inattentive';
        } else if (hasHyperactive) {
            subtype = 'hyperactive';
        }

        return {
            inattentiveScore,
            hyperactiveImpulsiveScore: hyperactiveScore,
            performanceIssues,
            subtype,
            email
        };
    };

    const progressPercentage = (Object.keys(answers).length / questions.length) * 100
    const isCurrentQuestionAnswered = answers.hasOwnProperty(currentQuestion)

    const scrollToQuestion = () => {
        if (questionRef.current) {
            const questionElement = questionRef.current
            const elementRect = questionElement.getBoundingClientRect()
            const absoluteElementTop = elementRect.top + window.pageYOffset
            const middle = absoluteElementTop - (window.innerHeight / 2) + (elementRect.height / 2)

            window.scrollTo({
                top: middle,
                behavior: 'smooth'
            })
        }
    }

    useEffect(() => {
        scrollToQuestion()
    }, [currentQuestion])

    const handleNavigation = (direction: 'prev' | 'next') => {
        if (direction === 'prev' && currentQuestion > 0) {
            setCurrentQuestion(prev => prev - 1)
            setShowError(false)
        } else if (direction === 'next') {
            if (!isCurrentQuestionAnswered) {
                setShowError(true)
                return
            }
            if (currentQuestion < questions.length - 1) {
                setCurrentQuestion(prev => prev + 1)
                setShowError(false)
            }
        }
    }

    const handleAnswer = (questionId: number, answerValue: number) => {
        setAnswers(prev => ({
            ...prev,
            [currentQuestion]: answerValue
        }))
        setShowError(false)

        if (Object.keys(answers).length === questions.length - 1) {
            setIsComplete(true)
        }

        trackAction('answer_question', 'adhd_test', `q${questionId}`, answerValue)
    }

    const handleSubmitEmail = async () => {
        if (!email || isSubmitting) return;

        setIsSubmitting(true);
        setSubmitError(null);

        const scores = calculateScore();

        try {
            const response = await fetch('/api/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'ADHD',
                    email: email,
                    scores: {
                        inattentiveScore: scores.inattentiveScore,
                        hyperactiveImpulsiveScore: scores.hyperactiveImpulsiveScore,
                        combinedScore: scores.inattentiveScore + scores.hyperactiveImpulsiveScore,
                        performanceIssues: scores.performanceIssues,
                        subtype: scores.subtype
                    },
                    answers: answers
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save test results');
            }

            const data = await response.json();

            if (!data.test?.id) {
                throw new Error('Invalid response: missing test ID');
            }

            trackAction('complete_test', 'adhd_test', 'submit');
            router.push(`/adhd-test/${data.test.id}/analysis`);
        } catch (error) {
            console.error('Error saving test results:', error);
            setSubmitError(t('completion.errorSubmitting'));
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-8">
            <div className="bg-bg-100 rounded-lg shadow-lg p-4 sm:p-6">
                {!isComplete ? (
                    <motion.div
                        ref={questionRef}
                        key={currentQuestion}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        className="space-y-4 sm:space-y-6"
                    >
                        <div className="mb-6 sm:mb-8">
                            {/* Question Number */}
                            <div className="text-sm text-primary-100 mb-2 font-medium">
                                {t('navigation.questionProgress', {
                                    current: currentQuestion + 1,
                                    total: questions.length
                                })}
                            </div>

                            {/* Question Text */}
                            <h3 className="text-lg sm:text-xl font-semibold text-text-100 mb-4 leading-relaxed">
                                {questions[currentQuestion].text}
                            </h3>

                            {/* Options */}
                            <div className="space-y-2 sm:space-y-3">
                                {questions[currentQuestion].options.map((option, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleAnswer(questions[currentQuestion].id, index)}
                                        className={`w-full text-left p-3 sm:p-4 rounded-lg border transition-colors
                                            ${answers[currentQuestion] === index
                                                ? 'border-primary-100 bg-primary-300/20 text-primary-100'
                                                : 'border-bg-300 hover:bg-primary-300/10 hover:border-primary-200'
                                            }
                                            text-base sm:text-lg leading-relaxed`}
                                    >
                                        {option}
                                    </button>
                                ))}
                            </div>

                            {/* Error Message */}
                            {showError && (
                                <motion.div
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="mt-3 text-accent-200 text-sm px-1"
                                >
                                    {t('navigation.pleaseSelectAnOption')}
                                </motion.div>
                            )}
                        </div>

                        {/* Progress and Navigation */}
                        <div className="space-y-4 mt-auto">
                            {/* Progress Bar */}
                            <div className="flex items-center gap-3">
                                <div className="flex-1 h-2 bg-bg-200 rounded-full overflow-hidden">
                                    <div
                                        className="h-full bg-primary-100 transition-all duration-300"
                                        style={{ width: `${progressPercentage}%` }}
                                    />
                                </div>
                                <div className="text-sm font-medium text-text-200 min-w-[3rem] text-right">
                                    {t('navigation.progressPercentage', {
                                        percent: Math.round(progressPercentage)
                                    })}
                                </div>
                            </div>

                            {/* Navigation Buttons */}
                            <div className="flex justify-between mt-6">
                                {currentQuestion > 0 && (
                                    <button
                                        onClick={() => handleNavigation('prev')}
                                        className="flex-1 py-3 px-6 text-text-200 bg-bg-100 rounded-lg 
                                                 hover:bg-bg-200 transition-colors duration-200 mr-3
                                                 text-base font-medium border border-bg-300"
                                    >
                                        {t('navigation.previous')}
                                    </button>
                                )}

                                {currentQuestion < questions.length - 1 && (
                                    <button
                                        onClick={() => handleNavigation('next')}
                                        className="flex-1 py-3 px-6 bg-primary-100 text-bg-100 rounded-lg 
                                                 hover:bg-primary-200 transition-colors duration-200
                                                 text-base font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled={!isCurrentQuestionAnswered}
                                    >
                                        {t('navigation.next')}
                                    </button>
                                )}
                            </div>
                        </div>
                    </motion.div>
                ) : (
                    // Completion Section
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="space-y-4 sm:space-y-6 p-2"
                    >
                        <h3 className="text-xl sm:text-2xl font-semibold mb-4 text-text-100">
                            {t('completion.title')}
                        </h3>
                        <p className="text-text-200 text-base sm:text-lg leading-relaxed">
                            {t('completion.emailPrompt')}
                        </p>
                        <input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder={t('completion.emailPlaceholder')}
                            className="w-full p-3 sm:p-4 border border-bg-300 rounded-lg 
                                text-base sm:text-lg focus:outline-none focus:border-primary-100
                                text-text-100 placeholder:text-text-200"
                        />
                        <button
                            onClick={handleSubmitEmail}
                            className="w-full p-3 sm:p-4 bg-primary-100 text-bg-100 rounded-lg 
                                hover:bg-primary-200 text-base sm:text-lg font-medium
                                transition-colors duration-200"
                        >
                            {t('completion.getReport')}
                        </button>
                    </motion.div>
                )}
            </div>

            {/* Disclaimer */}
            <div className="px-1 sm:px-2">
                <p className="text-text-200/80 text-xs sm:text-sm leading-relaxed text-center max-w-2xl mx-auto">
                    {t('disclaimer')}
                </p>
            </div>
        </div>
    )
} 