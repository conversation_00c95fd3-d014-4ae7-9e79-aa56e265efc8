'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { Check, Sparkles } from 'lucide-react';
import { cn } from '@/helpers/utils';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useTracking } from '@/hooks/useTracking';

interface TestType {
    type: string;
    title: string;
    description: string;
    usersChoicePercentage: number;
    features: {
        oneTime: { icon: React.ReactNode; text: string }[];
        subscription: { icon: React.ReactNode; text: string }[];
    };
}

const TEST_CONFIG: Record<string, TestType> = {
    default: {
        type: 'default',
        title: 'Unlock Your Professional Assessment Report',
        description: 'Get clinically-informed insights and evidence-based strategies tailored to your needs',
        usersChoicePercentage: 68,
        features: {
            oneTime: [
                { icon: <Check className="h-4 w-4" />, text: "AI-powered comprehensive analysis" },
                { icon: <Check className="h-4 w-4" />, text: "Detailed assessment breakdown" },
                { icon: <Check className="h-4 w-4" />, text: "Research-backed strategies" },
                { icon: <Check className="h-4 w-4" />, text: "Performance assessment" },
            ],
            subscription: [
                { icon: <Sparkles className="h-4 w-4" />, text: "Unlimited assessments & tracking" },
                { icon: <Check className="h-4 w-4" />, text: "Advanced progress monitoring" },
                { icon: <Check className="h-4 w-4" />, text: "Personalized recommendations" },
                { icon: <Check className="h-4 w-4" />, text: "Professional reports for healthcare" },
                { icon: <Check className="h-4 w-4" />, text: "Priority access to new features" },
            ]
        }
    }
};

const BestValueBadge = ({ percentage }: { percentage: number }) => {
    return (
        <div className="absolute -top-4 sm:-top-3 left-0 right-0 flex flex-col items-center">
            <motion.div
                className="inline-flex items-center gap-1.5 px-3 sm:px-4 py-1 sm:py-1.5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full shadow-sm"
                initial={{ y: 0 }}
                animate={{ y: [-2, 2, -2] }}
                transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                }}
            >
                <Sparkles className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-white" />
                <span className="text-[10px] sm:text-xs font-semibold text-white whitespace-nowrap">
                    MOST POPULAR
                </span>
            </motion.div>
            <div className="mt-1 sm:mt-1.5 text-[10px] sm:text-[11px] font-medium text-gray-600">
                {percentage}% users&apos; choice
            </div>
        </div>
    );
};

interface PriceCardProps {
    isPopular?: boolean;
    title: string;
    subtitle: string;
    price: string;
    period?: string;
    saveText?: string;
    cancelNote?: string;
    features: { icon: React.ReactNode; text: string }[];
    buttonText: string;
    buttonGradient?: boolean;
    onClick: () => void;
    disabled?: boolean;
    isGenerating?: boolean;
    usersChoicePercentage: number;
}

const PriceCard = ({
    isPopular,
    title,
    subtitle,
    price,
    period,
    saveText,
    cancelNote,
    features,
    buttonText,
    buttonGradient = false,
    onClick,
    disabled,
    isGenerating,
    usersChoicePercentage
}: PriceCardProps) => {
    return (
        <div className={cn(
            "bg-white rounded-xl p-4 sm:p-6 relative transition-all duration-300 hover:scale-[1.02]",
            isPopular
                ? "border-2 border-blue-200 shadow-lg"
                : "border border-gray-200 shadow-sm hover:border-gray-300"
        )}>
            {isPopular && <BestValueBadge percentage={usersChoicePercentage} />}

            <div className={cn("flex flex-col h-full", isPopular && "pt-8 sm:pt-12")}>
                <div className="flex flex-col mb-4 sm:mb-6">
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-900">{title}</h3>
                    <p className="text-xs sm:text-sm text-gray-600 mt-1">{subtitle}</p>
                </div>

                <div className="flex items-baseline mb-4 sm:mb-6">
                    <span className={cn(
                        "text-2xl sm:text-4xl font-bold tracking-tight",
                        isPopular ? "bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent" : "text-gray-900"
                    )}>
                        {price}
                    </span>
                    {period && (
                        <span className="text-xs sm:text-sm text-gray-500 ml-1 sm:ml-2">{period}</span>
                    )}
                </div>

                {saveText && (
                    <div className="text-xs sm:text-sm text-emerald-600 font-medium -mt-2 sm:-mt-4 mb-4 sm:mb-6">
                        {saveText}
                    </div>
                )}

                {cancelNote && (
                    <div className="text-xs sm:text-sm text-gray-500 -mt-2 sm:-mt-4 mb-4 sm:mb-6">
                        {cancelNote}
                    </div>
                )}

                <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8 flex-grow">
                    {features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2 sm:gap-3">
                            <div className={cn(
                                "flex-shrink-0 mt-0.5",
                                isPopular ? "text-purple-500" : "text-blue-500"
                            )}>
                                {feature.icon}
                            </div>
                            <span className="text-xs sm:text-sm text-gray-600">{feature.text}</span>
                        </li>
                    ))}
                </ul>

                <button
                    onClick={onClick}
                    disabled={disabled}
                    className={cn(
                        "w-full py-2.5 sm:py-3 rounded-lg font-medium transition-all flex items-center justify-center gap-2 text-sm sm:text-base",
                        buttonGradient
                            ? "bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white shadow-sm hover:shadow-md"
                            : "bg-blue-500 hover:bg-blue-600 text-white",
                        disabled && "opacity-50 cursor-not-allowed"
                    )}
                >
                    {disabled && (
                        <div className="animate-spin rounded-full h-3.5 w-3.5 sm:h-4 sm:w-4 border-2 border-white border-t-transparent" />
                    )}
                    {buttonText}
                </button>

                {isGenerating && (
                    <p className="text-[10px] sm:text-xs text-gray-500 text-center mt-2">
                        Please wait while we generate your report...
                    </p>
                )}
            </div>
        </div>
    );
};

interface Test {
    id: string;
    email: string;
    type: string;
    scores: any;
    analysis?: {
        id: string;
        content: string;
        isLocked: boolean;
    };
}

interface PricingSectionProps {
    isLocked: boolean;
    pricingSectionRef: React.RefObject<HTMLDivElement>;
    testType: string;
    isGenerating?: boolean;
}

const mapFeatures = (features: string[]) => features.map(text => ({
    icon: <Check className="h-4 w-4" />,
    text
}));

export const PricingSection: React.FC<PricingSectionProps> = ({
    isLocked,
    pricingSectionRef,
    testType,
    isGenerating = false
}) => {
    const { test_id } = useParams();
    const [error, setError] = useState('');
    const [test, setTest] = useState<Test | null>(null);
    const [isCheckingOut, setIsCheckingOut] = useState<'payment' | 'subscription' | null>(null);
    const t = useTranslations('components.pricing');
    const { trackAction } = useTracking();

    useEffect(() => {
        const fetchTestData = async () => {
            try {
                const response = await fetch(`/api/test?id=${test_id}`);
                if (response.ok) {
                    const data = await response.json();
                    setTest(data);
                } else {
                    throw new Error(t('errors.loadDataFailed'));
                }
            } catch (error) {
                console.error('Error fetching test data:', error);
                const errorMessage = error instanceof Error ? error.message : String(error);
                trackAction('pricing_error', 'error', 'load_data_failed', 0, { error: errorMessage });
                setError(t('errors.loadDataFailed'));
            }
        };

        if (test_id) {
            fetchTestData();
        }
    }, [test_id, t, trackAction]);

    const handleCheckout = async (mode: 'payment' | 'subscription') => {
        try {
            setIsCheckingOut(mode);
            if (!test?.email) {
                const errorMsg = t('errors.emailNotFound');
                trackAction('pricing_error', 'error', 'email_not_found', 0, { testId: test_id });
                setError(errorMsg);
                return;
            }

            trackAction('checkout_start', 'pricing', mode, 1, {
                testId: test_id,
                testType,
                mode
            });

            const priceId = mode === 'payment'
                ? process.env.NEXT_PUBLIC_STRIPE_PAYMENT_PRICE_ID
                : process.env.NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID;

            const response = await fetch('/api/stripe/create-checkout-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-project-identifier': process.env.NEXT_PUBLIC_PROJECT_IDENTIFIER || '',
                },
                body: JSON.stringify({
                    testId: test_id,
                    priceId,
                    mode,
                    email: test.email,
                    testType,
                }),
            });

            const { url } = await response.json();
            if (url) {
                trackAction('checkout_redirect', 'pricing', mode, 1, {
                    testId: test_id,
                    testType
                });
                window.location.href = url;
            }
        } catch (error) {
            console.error('Error creating checkout session:', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            trackAction('pricing_error', 'error', 'checkout_failed', 0, {
                error: errorMessage,
                testId: test_id,
                mode
            });
            setError(t('errors.checkoutFailed'));
        } finally {
            setIsCheckingOut(null);
        }
    };

    if (!isLocked) return null;

    return (
        <div ref={pricingSectionRef} className="space-y-4 sm:space-y-6 scroll-mt-16 max-w-5xl mx-auto px-4 sm:px-6">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 text-center sm:hidden">
                {t('unlockTitle')}
            </h2>

            <div className="hidden sm:block bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-6 sm:p-8 text-center">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                    {t('title')}
                </h2>
                <p className="text-base text-gray-600 max-w-2xl mx-auto">
                    {t('description')}
                </p>
            </div>

            {error && (
                <div className="text-red-500 text-sm text-center bg-red-50 rounded-lg p-3">
                    {error}
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                <PriceCard
                    title={t('pricingCards.oneTime.title')}
                    subtitle={t('pricingCards.oneTime.subtitle')}
                    price="$25"
                    features={mapFeatures(t.raw('pricingCards.oneTime.features'))}
                    buttonText={isGenerating ? t('pricingCards.oneTime.buttonTextGenerating') : t('pricingCards.oneTime.buttonText')}
                    onClick={() => handleCheckout('payment')}
                    disabled={isGenerating || isCheckingOut === 'payment'}
                    isGenerating={isGenerating}
                    usersChoicePercentage={70}
                />

                <PriceCard
                    isPopular
                    title={t('pricingCards.subscription.title')}
                    subtitle={t('pricingCards.subscription.subtitle')}
                    price="$16"
                    period={t('pricingCards.subscription.period')}
                    saveText={t('pricingCards.subscription.saveText')}
                    cancelNote={t('pricingCards.subscription.cancelNote')}
                    features={mapFeatures(t.raw('pricingCards.subscription.features'))}
                    buttonText={isGenerating ? t('pricingCards.subscription.buttonTextGenerating') : t('pricingCards.subscription.buttonText')}
                    buttonGradient
                    onClick={() => handleCheckout('subscription')}
                    disabled={isGenerating || isCheckingOut === 'subscription'}
                    isGenerating={isGenerating}
                    usersChoicePercentage={TEST_CONFIG['default'].usersChoicePercentage}
                />
            </div>
        </div>
    );
}; 