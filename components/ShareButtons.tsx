'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>nstagram, FaTiktok, FaFacebookF, FaLinkedinIn } from 'react-icons/fa6';
import { SiThreads } from 'react-icons/si';

export default function ShareButtons() {
    const shareLinks = [
        { name: 'Twitter', icon: FaXTwitter, url: 'https://twitter.com/intent/tweet?url=' },
        { name: 'Instagram', icon: FaInstagram, url: 'https://www.instagram.com/' },
        { name: 'TikTok', icon: FaTiktok, url: 'https://www.tiktok.com/' },
        { name: 'Facebook', icon: FaFacebookF, url: 'https://www.facebook.com/sharer/sharer.php?u=' },
        { name: 'Threads', icon: SiThreads, url: 'https://www.threads.net/' },
        { name: 'LinkedIn', icon: FaLinkedinIn, url: 'https://www.linkedin.com/sharing/share-offsite/?url=' },
    ];

    return (
        <div className="flex items-center justify-center flex-wrap gap-2">
            <span className="text-sm font-medium mr-2">Share:</span>
            {shareLinks.map((platform) => (
                <button
                    key={platform.name}
                    onClick={() => window.open(platform.url + encodeURIComponent(window.location.href), '_blank')}
                    className="flex items-center justify-center w-8 h-8 bg-purple-600 text-white rounded-full shadow-md transition-all hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:ring-offset-2"
                    aria-label={`Share on ${platform.name}`}
                >
                    <platform.icon className="w-4 h-4" />
                </button>
            ))}
        </div>
    );
}