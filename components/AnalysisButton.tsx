'use client'

import { useState, useRef, useEffect } from 'react';
import { UserCircle, History } from 'lucide-react'
import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';

export default function AnalysisButton() {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const router = useRouter();
    const t = useTranslations('components.analysisButton');

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className="relative" ref={dropdownRef}>
            <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="p-1 sm:p-2 rounded-md hover:bg-gray-100 transition-colors focus:outline-none flex items-center justify-center"
                aria-label={t('userMenuLabel')}
            >
                <UserCircle className="h-5 w-5 sm:h-6 sm:w-6" />
                <span className="hidden md:inline ml-2 text-sm font-medium">{t('analysisText')}</span>
                <svg className="hidden md:inline w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
            </button>

            {isDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    <button
                        onClick={() => {
                            setIsDropdownOpen(false);
                            router.push('/analysis');
                        }}
                        className="flex items-center space-x-2 w-full text-left px-4 py-2 text-sm text-[#4A4A4A] hover:bg-[#F7F7F7]"
                    >
                        <span className="text-xl">📊</span>
                        <span>{t('myAnalysis')}</span>
                    </button>
                </div>
            )}
        </div>
    );
} 