import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';

export const locales = ['en', 'zh', 'de', 'es', 'ar', 'ja', 'ko', 'fr', 'pt', 'nl', 'pl', 'tw'] as const;

export const routing = defineRouting({
    locales,
    defaultLocale: 'en',
    localeDetection: false,
    localePrefix: 'as-needed',
    pathnames: {
        '/': '/',
        '/privacy-policy': '/privacy-policy',
        '/terms-of-service': '/terms-of-service',
        '/disclaimer': '/disclaimer',
        '/refund-policy': '/refund-policy',
        '/raads-r-test': '/raads-r-test',
        '/adhd-test': '/adhd-test',
        '/analysis': '/analysis',
        '/raads-r-test/[test_id]/analysis': '/raads-r-test/[test_id]/analysis',
        '/adhd-test/[test_id]/analysis': '/adhd-test/[test_id]/analysis'
    }
});

export type Pathnames = keyof typeof routing.pathnames;
export type Locale = (typeof routing.locales)[number];

export const { Link, getPathname, redirect, usePathname, useRouter } =
    createNavigation(routing);

export const fullLanguageNames = {
    en: 'English',
    de: 'Deutsch',
    zh: '简体中文',
    tw: '繁體中文',
    ja: '日本語',
    ko: '한국어',
    fr: 'Français',
    pt: 'Português',
    es: 'Español',
    vi: 'Tiếng Việt',
    ar: 'العربية',
    nl: 'Nederlands',
    pl: 'Polski'
} as const;