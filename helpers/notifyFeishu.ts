import axios from 'axios';

const FEISHU_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL as string;
const FEISHU_KEY_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL as string;

async function sendFeishuNotification(webhookUrl: string, message: string) {
    if (!webhookUrl) {
        console.error('飞书 webhook URL 未设置');
        return;
    }

    try {
        await axios.post(webhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });
    } catch (error) {
        console.error('飞书通知发送失败');
    }
}

export const notifyFeishu = {
    notify: (message: string) => sendFeishuNotification(FEISHU_WEBHOOK_URL, message),
    keyNotify: (message: string) => sendFeishuNotification(FEISHU_KEY_WEBHOOK_URL, message),
};