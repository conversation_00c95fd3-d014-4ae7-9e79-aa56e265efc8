export interface SubMenuItem {
    name: string;
    link: string;
}

export interface MenuItem {
    name: string;
    color: string;
    link: string;
    subItems: SubMenuItem[];
}

export const menuItems: MenuItem[] = [
    {
        name: 'alive',
        color: 'soft-green',
        link: '/alive',
        subItems: [
            { name: 'AI Health Consultant', link: '/alive/ai-health-consultant' },
        ]
    },
    {
        name: 'achieve',
        color: 'soft-blue',
        link: '/achieve',
        subItems: [
            { name: 'AI Humanize', link: '/achieve/ai-humanize' },
            { name: 'AI Paragraph Generator', link: '/achieve/ai-paragraph-generator' },
        ]
    },
    {
        name: 'amuse',
        color: 'soft-orange',
        link: '/amuse',
        subItems: [
            { name: 'AI Hugging', link: '/amuse/image-to-video/ai-hugging' },
            { name: 'Make Girl Dance', link: '/amuse/image-to-video/girl-dance' },
            { name: 'Text to Image', link: '/amuse/text-to-image' },
            { name: 'Text to Video', link: '/amuse/text-to-video' },
            { name: 'Image to Video', link: '/amuse/image-to-video' },
            { name: 'Text to Music', link: '/amuse/text-to-music' },
        ]
    }
];