import axios from 'axios';

interface OpenRouterMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}

interface OpenRouterRequest {
    model: string;
    messages: OpenRouterMessage[];
    stream: boolean;
    max_tokens?: number;
    temperature?: number;
    top_p?: number;
}

interface OpenRouterStreamChunk {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        delta: {
            content?: string;
            role?: string;
        };
        finish_reason?: string;
    }>;
}

export class OpenRouterClient {
    private apiKey: string;
    private baseURL = 'https://openrouter.ai/api/v1';

    constructor(apiKey: string) {
        this.apiKey = apiKey;
    }

    async *generateStream(prompt: string, model: string): AsyncGenerator<string, void, unknown> {
        const requestData: OpenRouterRequest = {
            model,
            messages: [{ role: 'user', content: prompt }],
            stream: true,
            max_tokens: 4096,
            temperature: 0.8,
            top_p: 0.9,
        };

        try {
            const response = await axios.post(
                `${this.baseURL}/chat/completions`,
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
                        'X-Title': 'Autism Test Free - AI Analysis',
                    },
                    responseType: 'stream',
                }
            );

            let buffer = '';
            
            for await (const chunk of response.data) {
                buffer += chunk.toString();
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim() === '') continue;
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            return;
                        }
                        
                        try {
                            const parsed: OpenRouterStreamChunk = JSON.parse(data);
                            const content = parsed.choices?.[0]?.delta?.content;
                            if (content) {
                                yield content;
                            }
                        } catch (error) {
                            console.error('Error parsing OpenRouter stream chunk:', error);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('OpenRouter API error:', error);
            if (axios.isAxiosError(error)) {
                const errorMessage = error.response?.data?.error?.message || error.message;
                throw new Error(`OpenRouter API error: ${errorMessage}`);
            }
            throw error;
        }
    }

    async generateComplete(prompt: string, model: string): Promise<string> {
        const requestData: OpenRouterRequest = {
            model,
            messages: [{ role: 'user', content: prompt }],
            stream: false,
            max_tokens: 4096,
            temperature: 0.8,
            top_p: 0.9,
        };

        try {
            const response = await axios.post(
                `${this.baseURL}/chat/completions`,
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
                        'X-Title': 'Autism Test Free - AI Analysis',
                    },
                }
            );

            return response.data.choices?.[0]?.message?.content || '';
        } catch (error) {
            console.error('OpenRouter API error:', error);
            if (axios.isAxiosError(error)) {
                const errorMessage = error.response?.data?.error?.message || error.message;
                throw new Error(`OpenRouter API error: ${errorMessage}`);
            }
            throw error;
        }
    }
}
