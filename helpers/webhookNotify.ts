import axios from 'axios';

// Webhook 通知类型
interface PaidAPINotification {
    domain: string;
    provider: string;
    model: string;
    status: 'success' | 'error';
    responseTime: number;
    prompt: string;
    response?: string;
    error?: string;
}

interface AIGenErrorNotification {
    domain: string;
    provider: string;
    model: string;
    errorType: string;
    endpoint: string;
    errorDetails: string;
}

// 获取当前域名
function getCurrentDomain(): string {
    if (typeof window !== 'undefined') {
        return window.location.hostname;
    }
    
    // 服务端获取域名
    if (process.env.NEXT_PUBLIC_SITE_URL) {
        try {
            return new URL(process.env.NEXT_PUBLIC_SITE_URL).hostname;
        } catch {
            return 'unknown';
        }
    }
    
    return 'localhost:3000';
}

// 发送飞书通知
async function sendFeishuNotification(webhookUrl: string, message: string) {
    if (!webhookUrl) {
        console.error('飞书 webhook URL 未设置');
        return;
    }

    try {
        await axios.post(webhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });
    } catch (error) {
        console.error('飞书通知发送失败:', error);
    }
}

// 付费 API 调用通知
export async function notifyPaidAPIUsage(data: PaidAPINotification) {
    const domain = getCurrentDomain();
    const webhookUrl = process.env.NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL;
    
    if (!webhookUrl) {
        console.error('付费 API Webhook URL 未设置');
        return;
    }

    const statusIcon = data.status === 'success' ? '✅' : '❌';
    const message = `
🔔 付费 API 调用记录
--------------------
🌐 域名: ${domain}
🤖 API 提供商: ${data.provider}
📱 模型: ${data.model}
${statusIcon} 状态: ${data.status}
⏱️ 响应时间: ${data.responseTime}ms
--------------------
📝 请求内容:
${data.prompt.substring(0, 500)}${data.prompt.length > 500 ? '...' : ''}
--------------------
${data.status === 'success' && data.response ? `
📄 响应内容:
${data.response.substring(0, 500)}${data.response.length > 500 ? '...' : ''}
--------------------` : ''}
${data.status === 'error' && data.error ? `
❌ 错误详情:
${data.error}
--------------------` : ''}
    `.trim();

    await sendFeishuNotification(webhookUrl, message);
}

// AI 生成错误通知
export async function notifyAIGenError(data: AIGenErrorNotification) {
    const domain = getCurrentDomain();
    const webhookUrl = process.env.NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL;
    
    if (!webhookUrl) {
        console.error('AI 生成错误 Webhook URL 未设置');
        return;
    }

    const message = `
🚨 AI 内容生成失败告警
--------------------
🌐 域名: ${domain}
🔧 API 提供商: ${data.provider}
🤖 模型: ${data.model}
⚠️ 错误类型: ${data.errorType}
📍 端点: ${data.endpoint}
--------------------
❌ 错误详情:
${data.errorDetails}
--------------------
🔄 Vertex AI 失败，将尝试 OpenRouter 降级
    `.trim();

    await sendFeishuNotification(webhookUrl, message);
}

// 降级成功通知
export async function notifyFallbackSuccess(originalProvider: string, fallbackProvider: string, model: string) {
    const domain = getCurrentDomain();
    const webhookUrl = process.env.NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL;
    
    if (!webhookUrl) {
        return;
    }

    const message = `
✅ AI 服务降级成功
--------------------
🌐 域名: ${domain}
❌ 原提供商: ${originalProvider} (失败)
✅ 降级提供商: ${fallbackProvider}
🤖 使用模型: ${model}
--------------------
🔄 服务已恢复正常
    `.trim();

    await sendFeishuNotification(webhookUrl, message);
}
