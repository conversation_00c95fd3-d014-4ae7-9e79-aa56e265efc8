import { VertexAIClient } from './vertexAIClient';
import { OpenRouterClient } from './openRouterClient';
import { notifyPaidAPIUsage, notifyAIGenError, notifyFallbackSuccess } from './webhookNotify';

// 默认模型配置
const DEFAULT_MODEL = 'google/gemini-2.0-flash-exp:free';
const FALLBACK_MODELS = [
    'google/gemini-2.0-flash-exp:free',
    'openai/gpt-4o-mini',
    'google/gemini-1.5-pro-latest',
    'anthropic/claude-3.5-sonnet'
];

interface AIGenerationResult {
    success: boolean;
    provider: string;
    model: string;
    content?: string;
    error?: string;
    responseTime: number;
}

export class AIServiceManager {
    private vertexAI: VertexAIClient;
    private openRouter: OpenRouterClient | null = null;

    constructor() {
        this.vertexAI = new VertexAIClient();
        
        const openRouterKey = process.env.OPENROUTER_API_KEY;
        if (openRouterKey) {
            this.openRouter = new OpenRouterClient(openRouterKey);
        }
    }

    async *generateStreamWithFallback(prompt: string): AsyncGenerator<string, void, unknown> {
        const startTime = Date.now();
        let lastError: Error | null = null;

        // 首先尝试 Vertex AI
        try {
            console.log('尝试使用 Vertex AI 生成内容...');
            let hasContent = false;
            
            for await (const content of this.vertexAI.generateStream(prompt)) {
                hasContent = true;
                yield content;
            }

            if (hasContent) {
                const responseTime = Date.now() - startTime;
                console.log(`Vertex AI 生成成功，响应时间: ${responseTime}ms`);
                
                // 发送成功通知
                await notifyPaidAPIUsage({
                    domain: this.getCurrentDomain(),
                    provider: 'vertex-ai',
                    model: 'gemini-2.0-flash',
                    status: 'success',
                    responseTime,
                    prompt,
                    response: '内容生成成功'
                });
                
                return;
            }
        } catch (error) {
            lastError = error as Error;
            const responseTime = Date.now() - startTime;
            
            console.error('Vertex AI 生成失败:', error);
            
            // 发送错误通知
            await notifyAIGenError({
                domain: this.getCurrentDomain(),
                provider: 'vertex-ai',
                model: 'gemini-2.0-flash',
                errorType: 'generation_error',
                endpoint: '/api/gemini',
                errorDetails: lastError.message
            });

            await notifyPaidAPIUsage({
                domain: this.getCurrentDomain(),
                provider: 'vertex-ai',
                model: 'gemini-2.0-flash',
                status: 'error',
                responseTime,
                prompt,
                error: lastError.message
            });
        }

        // 如果 Vertex AI 失败，尝试 OpenRouter 降级
        if (!this.openRouter) {
            throw new Error('OpenRouter API key not configured and Vertex AI failed');
        }

        console.log('Vertex AI 失败，尝试 OpenRouter 降级...');

        // 尝试 OpenRouter 的多个模型
        for (const model of FALLBACK_MODELS) {
            try {
                console.log(`尝试 OpenRouter 模型: ${model}`);
                const fallbackStartTime = Date.now();
                let hasContent = false;

                for await (const content of this.openRouter.generateStream(prompt, model)) {
                    hasContent = true;
                    yield content;
                }

                if (hasContent) {
                    const responseTime = Date.now() - fallbackStartTime;
                    console.log(`OpenRouter 降级成功，模型: ${model}，响应时间: ${responseTime}ms`);
                    
                    // 发送降级成功通知
                    await notifyFallbackSuccess('vertex-ai', 'openrouter', model);
                    
                    // 发送付费 API 使用通知
                    await notifyPaidAPIUsage({
                        domain: this.getCurrentDomain(),
                        provider: 'openrouter',
                        model,
                        status: 'success',
                        responseTime,
                        prompt,
                        response: '内容生成成功'
                    });
                    
                    return;
                }
            } catch (error) {
                console.error(`OpenRouter 模型 ${model} 失败:`, error);
                const responseTime = Date.now() - startTime;
                
                // 发送错误通知
                await notifyAIGenError({
                    domain: this.getCurrentDomain(),
                    provider: 'openrouter',
                    model,
                    errorType: 'generation_error',
                    endpoint: '/api/analysis/generate',
                    errorDetails: (error as Error).message
                });

                await notifyPaidAPIUsage({
                    domain: this.getCurrentDomain(),
                    provider: 'openrouter',
                    model,
                    status: 'error',
                    responseTime,
                    prompt,
                    error: (error as Error).message
                });
                
                lastError = error as Error;
                continue; // 尝试下一个模型
            }
        }

        // 所有模型都失败了
        throw new Error(`All AI providers failed. Last error: ${lastError?.message}`);
    }

    private getCurrentDomain(): string {
        if (process.env.NEXT_PUBLIC_SITE_URL) {
            try {
                return new URL(process.env.NEXT_PUBLIC_SITE_URL).hostname;
            } catch {
                return 'unknown';
            }
        }
        return 'localhost:3000';
    }
}
