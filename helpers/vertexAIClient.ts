import { VertexA<PERSON> } from '@google-cloud/vertexai';
import { GoogleAuth } from 'google-auth-library';

export class VertexAIClient {
    private vertexAI: VertexAI;
    private auth: GoogleAuth;

    constructor() {
        const keyFilename = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        
        this.auth = new GoogleAuth({
            keyFilename: keyFilename,
            scopes: ['https://www.googleapis.com/auth/cloud-platform']
        });

        this.vertexAI = new VertexAI({
            project: process.env.GOOGLE_CLOUD_PROJECT || '',
            location: process.env.GOOGLE_CLOUD_LOCATION || '',
            auth: this.auth
        } as any);
    }

    async *generateStream(prompt: string, model: string = 'gemini-2.0-flash'): AsyncGenerator<string, void, unknown> {
        try {
            // 确保认证
            const client = await this.auth.getClient();
            await client.getAccessToken();

            const generativeModel = this.vertexAI.preview.getGenerativeModel({
                model: model,
                generation_config: {
                    max_output_tokens: 4096,
                    temperature: 0.8,
                    top_p: 0.9,
                    top_k: 40,
                },
            } as any);

            const streamingResp = await generativeModel.generateContentStream({
                contents: [{ role: 'user', parts: [{ text: prompt }] }],
            });

            for await (const item of streamingResp.stream) {
                const content = item.candidates?.[0]?.content?.parts?.[0]?.text;
                if (content) {
                    yield content;
                }
            }
        } catch (error) {
            console.error('Vertex AI streaming error:', error);
            throw error;
        }
    }

    async generateComplete(prompt: string, model: string = 'gemini-2.0-flash'): Promise<string> {
        try {
            // 确保认证
            const client = await this.auth.getClient();
            await client.getAccessToken();

            const generativeModel = this.vertexAI.preview.getGenerativeModel({
                model: model,
                generation_config: {
                    max_output_tokens: 4096,
                    temperature: 0.8,
                    top_p: 0.9,
                    top_k: 40,
                },
            } as any);

            const result = await generativeModel.generateContent({
                contents: [{ role: 'user', parts: [{ text: prompt }] }],
            });

            return result.response.candidates?.[0]?.content?.parts?.[0]?.text || '';
        } catch (error) {
            console.error('Vertex AI generation error:', error);
            throw error;
        }
    }
}
