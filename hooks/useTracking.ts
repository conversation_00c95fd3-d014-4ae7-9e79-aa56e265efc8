import { useCallback } from 'react';
import { notifyFeishu } from '@/helpers/notifyFeishu';
import { logEvent } from '@/helpers/gaLog';
import { getIpInfo } from '@/helpers/ipTracker';

export function useTracking() {
    const trackAction = useCallback(async (
        action: string,
        category: string,
        label: string,
        value: number = 1,
        additionalInfo: Record<string, any> = {}
    ) => {
        // Track in GA
        logEvent(action, category, label, value);

        // Track in Feishu (non-blocking)
        try {
            const ipInfo = await getIpInfo();
            const timestamp = new Date().toISOString();

            const message = `
                🎯 User Action
                Action: ${action}
                Category: ${category}
                Label: ${label}
                Time: ${timestamp}
                IP: ${ipInfo?.ip || 'Unknown'}
                Location: ${ipInfo?.city || 'Unknown'}, ${ipInfo?.region || ''}, ${ipInfo?.country || ''}
                Additional Info: ${JSON.stringify(additionalInfo)}
            `.trim();

            notifyFeishu.notify(message);
        } catch (error) {
            console.error('Failed to track action:', error);
        }
    }, []);

    return { trackAction };
} 