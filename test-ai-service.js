// 简单的测试脚本来验证 AI 服务功能
// 运行: node test-ai-service.js

const axios = require('axios');

async function testAIService() {
    console.log('🧪 开始测试 AI 服务...');
    
    const testPrompt = "请简单介绍一下自闭症谱系障碍的特征。";
    
    try {
        console.log('📤 发送测试请求...');
        const response = await axios.post('http://localhost:3000/api/analysis/generate', {
            prompt: testPrompt
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            responseType: 'stream'
        });

        console.log('📥 接收流式响应...');
        let fullResponse = '';
        
        response.data.on('data', (chunk) => {
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') {
                        console.log('✅ 响应完成');
                        return;
                    }
                    try {
                        const parsed = JSON.parse(data);
                        if (parsed.content) {
                            process.stdout.write(parsed.content);
                            fullResponse += parsed.content;
                        } else if (parsed.error) {
                            console.error('\n❌ 错误:', parsed.error);
                        }
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
            }
        });

        response.data.on('end', () => {
            console.log('\n\n🎉 测试完成！');
            console.log('📊 响应长度:', fullResponse.length, '字符');
        });

        response.data.on('error', (error) => {
            console.error('❌ 流错误:', error);
        });

    } catch (error) {
        console.error('❌ 请求失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应:', error.response.data);
        }
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        await axios.get('http://localhost:3000');
        console.log('✅ 服务器正在运行');
        return true;
    } catch (error) {
        console.log('❌ 服务器未运行，请先启动: npm run dev');
        return false;
    }
}

async function main() {
    console.log('🚀 AI 服务测试工具');
    console.log('==================');
    
    const serverRunning = await checkServer();
    if (!serverRunning) {
        return;
    }
    
    await testAIService();
}

main().catch(console.error);
