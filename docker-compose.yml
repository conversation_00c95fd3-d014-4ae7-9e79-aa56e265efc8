version: '3.8'

services:
  autism_test:
    image: blowxian/autism_test:latest
    container_name: autism_test
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
    restart: always

  autism_test_db:
    image: postgres:alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: autism_test_admin
      POSTGRES_PASSWORD: autism_test_2024
      POSTGRES_DB: autism_test
    volumes:
      - ./data:/var/lib/postgresql/data # 持久化数据
    container_name: autism_test_db
