# AI 服务降级系统

## 概述

本项目已实现了完整的 AI 服务降级系统，当 Google Cloud Vertex AI 服务不可用时，自动切换到 OpenRouter API 作为备用方案，确保用户始终能够获得 AI 分析结果。

## 功能特性

### 1. 自动降级机制
- **主要服务**: Google Cloud Vertex AI (Gemini 2.0 Flash)
- **备用服务**: OpenRouter API
- **支持模型**: 
  - `google/gemini-2.0-flash-exp:free` (默认)
  - `openai/gpt-4o-mini`
  - `google/gemini-1.5-pro-latest`
  - `anthropic/claude-3.5-sonnet`

### 2. Webhook 通知系统
- **付费 API 调用通知**: 记录所有 AI API 调用，包括成功和失败的请求
- **AI 生成错误通知**: 当 AI 生成过程出错时发送告警
- **域名识别**: 所有通知都包含当前域名信息
- **飞书集成**: 通过飞书 webhook 发送实时通知

### 3. 流式响应支持
- 保持原有的流式响应体验
- 支持 Vertex AI 和 OpenRouter 的流式输出
- 错误处理和重试机制

## 文件结构

```
helpers/
├── aiServiceManager.ts      # AI 服务管理器，处理降级逻辑
├── vertexAIClient.ts        # Vertex AI 客户端封装
├── openRouterClient.ts      # OpenRouter 客户端实现
└── webhookNotify.ts         # Webhook 通知服务

app/api/analysis/generate/
└── route.ts                 # 更新的 AI 生成 API 路由
```

## 环境变量配置

需要在 `.env` 文件中添加以下配置：

```bash
# OpenRouter API Key (必需)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 飞书 Webhook URLs (已存在)
NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=your_feishu_webhook_url
NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=your_feishu_key_webhook_url

# 站点 URL (用于域名识别)
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## 通知格式

### 付费 API 调用通知
```
🔔 付费 API 调用记录
--------------------
🌐 域名: autismtestfree.com
🤖 API 提供商: vertex-ai
📱 模型: gemini-2.0-flash
✅ 状态: 成功
⏱️ 响应时间: 1865ms
--------------------
📝 请求内容:
As an expert in autism spectrum analysis...
--------------------
📄 响应内容:
Okay, here's a comprehensive analysis...
--------------------
```

### AI 生成错误通知
```
🚨 AI 内容生成失败告警
--------------------
🌐 域名: localhost:3000
🔧 API 提供商: vertex-ai
🤖 模型: gemini-2.0-flash
⚠️ 错误类型: generation_error
📍 端点: /api/gemini
--------------------
❌ 错误详情:
Vertex AI error: [VertexAI.ClientError]: got status: 404 Not Found...
--------------------
🔄 Vertex AI 失败，将尝试 OpenRouter 降级
```

## 工作流程

1. **用户请求**: 用户发起 AI 分析请求
2. **Vertex AI 尝试**: 首先尝试使用 Google Cloud Vertex AI
3. **成功处理**: 如果成功，发送付费 API 使用通知
4. **失败处理**: 如果失败，发送错误通知并启动降级
5. **OpenRouter 降级**: 依次尝试配置的备用模型
6. **降级成功**: 发送降级成功通知和付费 API 使用通知
7. **完全失败**: 如果所有服务都失败，返回错误信息

## 测试

运行测试脚本验证功能：

```bash
# 启动开发服务器
npm run dev

# 在另一个终端运行测试
node test-ai-service.js
```

## 监控和维护

- 通过飞书通知监控 API 使用情况
- 关注错误通知，及时处理服务问题
- 定期检查 OpenRouter API 配额和使用情况
- 监控响应时间和成功率

## 注意事项

1. **API Key 安全**: 确保 OpenRouter API Key 安全存储
2. **配额管理**: 监控 OpenRouter API 使用配额
3. **成本控制**: OpenRouter 为付费服务，注意成本控制
4. **性能监控**: 关注降级对响应时间的影响
5. **错误处理**: 确保所有错误都有适当的处理和通知
