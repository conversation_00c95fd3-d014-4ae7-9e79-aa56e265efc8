import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          100: '#0077C2',
          200: '#59a5f5',
          300: '#c8ffff',
        },
        accent: {
          100: '#00BFFF',
          200: '#00619a',
        },
        text: {
          100: '#333333',
          200: '#5c5c5c',
        },
        bg: {
          100: '#FFFFFF',
          200: '#f5f5f5',
          300: '#cccccc',
        }
      },
      animation: {
        'spin-and-scale': 'spin-scale 6s linear infinite',
        'spin-slow': 'spin 20s linear infinite',
        'pulse': 'pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        'spin-scale': {
          '0%': { transform: 'rotate(0deg) scale(1)' },
          '25%': { transform: 'rotate(90deg) scale(1.3)' },
          '50%': { transform: 'rotate(180deg) scale(1)' },
          '75%': { transform: 'rotate(270deg) scale(1.3)' },
          '100%': { transform: 'rotate(360deg) scale(1)' },
        },
        pulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0' },
        },
      },
      fontFamily: {
        'chakra': ['Chakra Petch', 'sans-serif'],
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    // ... other plugins
  ],
  safelist: [
    'border-primary-100',
    'border-primary-200',
    'border-primary-300',
    'border-accent-100',
    'border-accent-200',
  ]
};

export default config;
