// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Test {
  id        String    @id @default(cuid())
  type      TestType
  email     String    @db.Text
  scores    Json
  answers   Json // Store all question answers
  createdAt DateTime  @default(now())
  analysis  Analysis?

  @@index([email])
  @@index([type])
}

model Analysis {
  id        String   @id @default(cuid())
  content   String   @db.Text
  isLocked  Boolean  @default(true)
  createdAt DateTime @default(now())
  testId    String   @unique
  test      Test     @relation(fields: [testId], references: [id], onDelete: Cascade)
  payment   Payment?
}

model Payment {
  id         String   @id @default(cuid())
  stripeId   String   @unique
  amount     Int
  currency   String
  status     String
  mode       String   @default("payment") // 'payment' or 'subscription'
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  analysisId String   @unique
  analysis   Analysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)

  // Subscription specific fields
  subscriptionId     String?   @unique // Stripe subscription ID
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  canceledAt         DateTime?

  @@index([status])
  @@index([mode])
}

enum TestType {
  RAADS
  ADHD
}
