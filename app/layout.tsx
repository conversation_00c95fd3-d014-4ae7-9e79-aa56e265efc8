import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale } from 'next-intl/server';
import "./[locale]/globals.css";
import Header from "@/components/ui/Header";
import Footer from "@/components/ui/Footer"
import <PERSON>ript from 'next/script';
import { Locale, locales } from '@/i18n/routing';
import { ReactNode } from 'react';
import PageViewTracker from '@/components/PageViewTracker';

const inter = Inter({ subsets: ["latin"] });

type Props = {
    children: ReactNode;
    params: { locale: string } | Promise<{ locale: string }>;
};

export function generateStaticParams() {
    return locales.map((locale) => ({ locale }));
}

export const metadata: Metadata = {
    title: "Free Online RAADS-R Test | Autism Spectrum Self-Assessment Tool",
    description: "Take the free online RAADS-R (Ritvo Autism Asperger Diagnostic Scale-Revised) test. A scientifically validated screening tool for assessing autism spectrum traits in adults.",
    keywords: "RAADS-R test, autism test, autism spectrum assessment, free online autism test, autism screening tool, adult autism test, RAADS-R online",
    openGraph: {
        title: "Free Online RAADS-R Test | Autism Spectrum Self-Assessment",
        description: "Take the free online RAADS-R test - a scientifically validated screening tool for assessing autism spectrum traits in adults.",
        type: "website"
    },
    twitter: {
        card: "summary_large_image",
        title: "Free Online RAADS-R Test | Autism Spectrum Self-Assessment",
        description: "Take the free online RAADS-R test - a scientifically validated screening tool for assessing autism spectrum traits in adults."
    }
};

// Add GA Script component with performance optimizations
const GoogleAnalytics = () => {
    const gaId = process.env.NEXT_PUBLIC_GA_ID;
    const gtagId = 'AW-16758172378';

    return (
        <>
            <Script
                src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
                strategy="afterInteractive"
            />
            <Script
                src={`https://www.googletagmanager.com/gtag/js?id=${gtagId}`}
                strategy="afterInteractive"
            />
            <Script id="google-analytics" strategy="afterInteractive">
                {`
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag('js', new Date());
                    gtag('config', '${gaId}');
                    gtag('config', '${gtagId}');
                `}
            </Script>
        </>
    );
};

export default async function LocaleLayout({
    children,
    params,
}: Props) {
    // Resolve params
    const resolvedParams = await (Promise.resolve(params));
    const locale = String(resolvedParams.locale);

    // Enable static rendering
    setRequestLocale(locale);

    // Get messages for the locale
    const messages = await getMessages();

    return (
        <html lang={locale}>
            <body className={`${inter.className} min-h-screen flex flex-col bg-gradient-to-b from-primary-100/5 to-bg-100`}>
                <NextIntlClientProvider messages={messages}>
                    <Header />
                    <main className="flex-grow container mx-auto px-4 py-6 mt-[72px] sm:mt-[80px]">
                        {children}
                    </main>
                    <Footer />
                    <PageViewTracker />
                </NextIntlClientProvider>
                <GoogleAnalytics />
            </body>
        </html>
    );
}