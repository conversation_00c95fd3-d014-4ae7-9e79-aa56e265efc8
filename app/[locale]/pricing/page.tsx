// app/[locale]/pricing/page.tsx
import { getTranslations } from 'next-intl/server';
import PricingComponent, { PricingOption } from './PageComponent';

export default async function PricingPage() {
    const t = await getTranslations('Pricing');

    const pricingOptions: PricingOption[] = [
        {
            title: t('basic'),
            monthlyPrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_SUBSCRIPTION_PRICE_ID || '', amount: '$9.99' },
            yearlyPrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_SUBSCRIPTION_PRICE_ID || '', amount: '$99.99' },
            perUsePrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_PAYMENT_PRICE_ID || '', amount: '$0.99' },
            features: [
                t('basicFeature1'),
                t('basicFeature2'),
                t('basicFeature3')
            ],
        },
        {
            title: t('pro'),
            monthlyPrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_SUBSCRIPTION_PRICE_ID || '', amount: '$19.99' },
            yearlyPrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_SUBSCRIPTION_PRICE_ID || '', amount: '$199.99' },
            perUsePrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_PAYMENT_PRICE_ID || '', amount: '$1.99' },
            features: [
                t('proFeature1'),
                t('proFeature2'),
                t('proFeature3'),
                t('proFeature4')
            ],
            recommended: true,
        },
        {
            title: t('enterprise'),
            monthlyPrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_SUBSCRIPTION_PRICE_ID || '', amount: '$49.99' },
            yearlyPrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_SUBSCRIPTION_PRICE_ID || '', amount: '$499.99' },
            perUsePrice: { id: process.env.NEXT_PUBLIC_STRIPE_TEST_PAYMENT_PRICE_ID || '', amount: '$4.99' },
            features: [
                t('enterpriseFeature1'),
                t('enterpriseFeature2'),
                t('enterpriseFeature3'),
                t('enterpriseFeature4'),
                t('enterpriseFeature5')
            ],
        },
    ];

    return (
        <main className="flex-grow container mx-auto px-4 py-12 bg-light-grayish-white dark:bg-dark-navy">
            <h1 className="text-4xl font-bold mb-8 text-center text-dark-gray dark:text-light-gray">{t('title')}</h1>
            <PricingComponent
                pricingOptions={pricingOptions}
                translations={{
                    monthly: t('monthly'),
                    yearly: t('yearly'),
                    perUse: t('perUse'),
                    year: t('year'),
                    use: t('use'),
                    month: t('month'),
                    selectPlan: t('selectPlan')
                }}
            />
        </main>
    );
}