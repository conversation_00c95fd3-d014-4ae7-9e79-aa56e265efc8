'use client'

import { useEffect, useState } from 'react';
import { loadStripe, Stripe } from '@stripe/stripe-js';

// Define types for pricing options and translations
export interface PricingOption {
    title: string;
    monthlyPrice: { id: string; amount: string };
    yearlyPrice: { id: string; amount: string };
    perUsePrice: { id: string; amount: string };
    features: string[];
    recommended?: boolean;
}

interface Translations {
    monthly: string;
    yearly: string;
    perUse: string;
    year: string;
    month: string;
    use: string;
    selectPlan: string;
}

// Update PricingOption component props
interface PricingOptionProps {
    title: string;
    price: string;
    period: string;
    features: string[];
    buttonText: string;
    recommended?: boolean;
    onSelectPlan: () => void;
    isLoading: boolean;
}

const PricingOption: React.FC<PricingOptionProps> = ({
    title,
    price,
    period,
    features,
    buttonText,
    recommended = false,
    onSelectPlan,
    isLoading
}) => (
    <div
        className={`bg-white dark:bg-dark-navy p-8 rounded-lg shadow-sm border transition-all duration-300 hover:shadow-md ${recommended ? 'border-soft-blue dark:border-sky-blue ring-2 ring-soft-blue dark:ring-sky-blue' : 'border-neutral-gray dark:border-light-gray'}`}>
        <h3 className={`text-2xl font-semibold mb-4 ${recommended ? 'text-soft-blue dark:text-sky-blue' : 'text-dark-gray dark:text-light-gray'}`}>{title}</h3>
        <div className="mb-6">
            <span className="text-4xl font-bold text-dark-gray dark:text-light-gray">{price}</span>
            <span className="text-neutral-gray dark:text-light-gray">/{period}</span>
        </div>
        <ul className="mb-8 space-y-2">
            {features.map((feature, index) => (
                <li key={index} className="flex items-center text-dark-gray dark:text-light-gray">
                    <svg className="h-5 w-5 text-soft-green dark:text-lime-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    {feature}
                </li>
            ))}
        </ul>
        <button
            className={`w-full py-2 px-4 rounded ${recommended ? 'bg-soft-blue dark:bg-sky-blue text-white hover:bg-soft-blue/80 dark:hover:bg-sky-blue/80' : 'bg-neutral-gray dark:bg-light-gray text-dark-gray dark:text-dark-navy hover:bg-neutral-gray/80 dark:hover:bg-light-gray/80'} transition-colors duration-300 flex items-center justify-center`}
            onClick={onSelectPlan}
            disabled={isLoading}
        >
            {isLoading ? <LoadingSpinner /> : buttonText}
        </button>
    </div>
);

// Update PricingComponent props
interface PricingComponentProps {
    pricingOptions: PricingOption[];
    translations: Translations;
}

const LoadingSpinner: React.FC = () => (
    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_TEST_PUBLIC_KEY as string);

function PricingComponent({ pricingOptions, translations }: PricingComponentProps) {
    const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly' | 'perUse'>('monthly');
    const [loadingPlan, setLoadingPlan] = useState<number | null>(null);

    useEffect(() => {
        const query = new URLSearchParams(window.location.search);
        if (query.get('success')) {
            console.log('订单已下单！您将收到一封确认电子邮件。');
        }
        if (query.get('canceled')) {
            console.log('订单已取消 -- 继续浏览并在准备好时结账。');
        }
    }, []);

    const handleSelectPlan = async (priceId: string, mode: 'payment' | 'subscription', planIndex: number) => {
        setLoadingPlan(planIndex);
        try {
            const response = await fetch('/api/stripe/create-checkout-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    price: { id: priceId, mode: mode },
                    quantity: 1,
                    metadata: {},
                    redirectUrl: window.location.href,
                }),
            });

            const { sessionId } = await response.json();
            const stripe = await stripePromise;
            if (stripe) {
                const { error } = await stripe.redirectToCheckout({ sessionId });
                if (error) {
                    console.error('错误:', error);
                }
            }
        } catch (error) {
            console.error('错误:', error);
        } finally {
            setLoadingPlan(null);
        }
    };

    return (
        <>
            <div className="mb-8 flex justify-center">
                <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                        type="button"
                        className={`px-4 py-2 text-sm font-medium rounded-l-lg ${billingPeriod === 'monthly' ? 'bg-soft-blue dark:bg-sky-blue text-white' : 'bg-white dark:bg-dark-navy text-dark-gray dark:text-light-gray hover:bg-neutral-gray dark:hover:bg-light-gray/20'}`}
                        onClick={() => setBillingPeriod('monthly')}
                    >
                        {translations.monthly}
                    </button>
                    <button
                        type="button"
                        className={`px-4 py-2 text-sm font-medium ${billingPeriod === 'yearly' ? 'bg-soft-blue dark:bg-sky-blue text-white' : 'bg-white dark:bg-dark-navy text-dark-gray dark:text-light-gray hover:bg-neutral-gray dark:hover:bg-light-gray/20'}`}
                        onClick={() => setBillingPeriod('yearly')}
                    >
                        {translations.yearly}
                    </button>
                    <button
                        type="button"
                        className={`px-4 py-2 text-sm font-medium rounded-r-lg ${billingPeriod === 'perUse' ? 'bg-soft-blue dark:bg-sky-blue text-white' : 'bg-white dark:bg-dark-navy text-dark-gray dark:text-light-gray hover:bg-neutral-gray dark:hover:bg-light-gray/20'}`}
                        onClick={() => setBillingPeriod('perUse')}
                    >
                        {translations.perUse}
                    </button>
                </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {pricingOptions.map((option, index) => (
                    <PricingOption
                        key={index}
                        title={option.title}
                        price={billingPeriod === 'yearly' ? option.yearlyPrice.amount : billingPeriod === 'perUse' ? option.perUsePrice.amount : option.monthlyPrice.amount}
                        period={billingPeriod === 'yearly' ? translations.year : billingPeriod === 'perUse' ? translations.use : translations.month}
                        features={option.features}
                        buttonText={translations.selectPlan}
                        recommended={option.recommended ?? false}
                        onSelectPlan={() => handleSelectPlan(
                            billingPeriod === 'yearly' ? option.yearlyPrice.id :
                                billingPeriod === 'perUse' ? option.perUsePrice.id :
                                    option.monthlyPrice.id,
                            billingPeriod === 'perUse' ? 'payment' : 'subscription',
                            index
                        )}
                        isLoading={loadingPlan === index}
                    />
                ))}
            </div>
        </>
    );
}

export { PricingOption };
export default PricingComponent;