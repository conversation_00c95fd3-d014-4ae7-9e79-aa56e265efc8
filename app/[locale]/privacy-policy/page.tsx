import { useTranslations } from 'next-intl';
import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Privacy Policy | Always Health',
    description: 'Learn how Always Health protects your privacy and handles your personal information. Our comprehensive privacy policy outlines data collection, usage, and your rights.',
};

export default function PrivacyPolicy() {
    const t = useTranslations('pages.PrivacyPolicy');

    return (
        <div className="max-w-4xl mx-auto px-4">
            <main className="py-8 md:py-12">
                <div className="space-y-8">
                    <div className="text-center mb-12">
                        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-text-100">
                            {t('title')}
                        </h1>
                        <p className="text-lg md:text-xl text-text-200">
                            {t('description')}
                        </p>
                    </div>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section1')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section1Para1')}</p>
                            <p className="text-text-200">{t('section1Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section2')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section2Para1')}</p>
                            <p className="text-text-200">{t('section2Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section3')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section3Para1')}</p>
                            <p className="text-text-200">{t('section3Para2')}</p>
                        </div>
                    </section>

                    <p className="text-sm text-text-200 text-center pt-4">
                        {t('lastUpdated')}
                    </p>
                </div>
            </main>
        </div>
    );
}