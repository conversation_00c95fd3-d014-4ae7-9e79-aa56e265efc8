import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import RAADSTest from '@/components/RAADSTest'
import ScoreInterpretation from '@/components/ScoreInterpretation'
import FAQSection from '@/components/FAQSection'
import Reference from '@/components/Reference'

export async function generateMetadata(): Promise<Metadata> {
    const t = await getTranslations('pages.raads-r-test.metadata')

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('ogTitle'),
            description: t('ogDescription'),
        },
    }
}

export default async function RAADSRPage() {
    const t = await getTranslations('pages.raads-r-test.hero')

    return (
        <main className="max-w-4xl mx-auto px-4">
            <div className="mx-auto space-y-6">
                <div className="top-0 bg-background-100/95 backdrop-blur-sm py-3 z-10">
                    <h2 className="text-center flex flex-col items-center gap-1 sm:gap-2">
                        <span className="text-primary-100 text-2xl sm:text-3xl font-bold">
                            {t('title')}
                        </span>
                        <span className="text-text-100 text-sm sm:text-base font-medium px-2">
                            {t('subtitle')}
                        </span>
                        <span className="text-text-100/80 text-xs sm:text-sm max-w-[85%] sm:max-w-[75%] text-center leading-relaxed">
                            {t('description')}
                        </span>
                    </h2>
                </div>

                <RAADSTest />
            </div>

            <section className="mt-12 bg-bg-100 rounded-lg p-6 border border-bg-300/10">
                <ScoreInterpretation />
            </section>

            <section className="mt-9 pt-3 bg-bg-100 rounded-lg p-6">
                <FAQSection />
            </section>

            <Reference
                citation="Ritvo, R. A., Ritvo, E. R., Guthrie, D., Ritvo, M. J., Hufnagel, D. H., McMahon, W., … & Eloff, J. (2011). The Ritvo Autism Asperger Diagnostic Scale-Revised (RAADS-R): A Scale to Assist the Diagnosis of Autism Spectrum Disorder in Adults: An International Validation Study. Journal of Autism and Developmental Disorders, 41(8), 1076-1089."
                link="https://link.springer.com/article/10.1007/s10803-010-1133-5"
            />
        </main>
    )
}
