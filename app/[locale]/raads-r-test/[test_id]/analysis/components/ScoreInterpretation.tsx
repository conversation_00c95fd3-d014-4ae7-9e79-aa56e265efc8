'use client'

import { useTranslations } from 'next-intl';

interface ScoreInterpretationProps {
    score: number;
}

export const ScoreInterpretation = ({ score }: ScoreInterpretationProps) => {
    const t = useTranslations('components.scoreInterpretation.raadsr');

    const getScoreCategory = (score: number) => {
        if (score <= 25) return { range: 'scores.0', color: 'green' };
        if (score <= 49) return { range: 'scores.1', color: 'blue' };
        if (score <= 64) return { range: 'scores.2', color: 'blue' };
        if (score <= 89) return { range: 'scores.3', color: 'yellow' };
        if (score <= 129) return { range: 'scores.4', color: 'yellow' };
        if (score <= 159) return { range: 'scores.5', color: 'orange' };
        if (score <= 226) return { range: 'scores.6', color: 'red' };
        return { range: 'scores.7', color: 'red' };
    };

    const getColorClass = (color: string) => {
        const colorMap: { [key: string]: string } = {
            green: "border-green-500",
            blue: "border-blue-500",
            yellow: "border-yellow-500",
            orange: "border-orange-500",
            red: "border-red-500"
        };
        return colorMap[color] || "border-gray-500";
    };

    const scoreCategory = getScoreCategory(score);

    return (
        <div className="mt-6 space-y-4">
            <div className="flex flex-col gap-2">
                <h3 className="text-lg font-semibold text-primary">
                    {t('title')}
                </h3>
                <p className="text-gray-600 text-sm">
                    {t('subtitle')}
                </p>
            </div>

            <div
                className={`bg-primary/[0.02] rounded-lg p-4 hover:bg-primary/[0.04] 
                           transition-colors duration-200 border-l-4 
                           ${getColorClass(scoreCategory.color)}`}
            >
                <h4 className="font-medium text-base text-primary mb-2">
                    {t.raw(scoreCategory.range).range}
                </h4>
                <p className="text-primary/70 text-sm leading-relaxed">
                    {t.raw(scoreCategory.range).description}
                </p>
            </div>
        </div>
    );
}; 