'use client'

import React, { useEffect, useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import { Lock } from 'lucide-react';
import { TestimonialCarousel } from './TestimonialCarousel';

interface AIAnalysisProps {
    isLocked: boolean;
    streamContent: string;
    isGenerating: boolean;
    scrollToPricing: () => void;
}

export const AIAnalysis: React.FC<AIAnalysisProps> = ({
    isLocked,
    streamContent,
    isGenerating,
    scrollToPricing
}) => {
    const t = useTranslations('pages.raadsrAnalysis');
    const shouldShowUnlockButton = isLocked || isGenerating;
    const containerRef = useRef<HTMLDivElement>(null);
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        if (!shouldShowUnlockButton) return;

        const checkVisibility = () => {
            if (!containerRef.current) return;
            const rect = containerRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportCenter = viewportHeight / 2;
            const buffer = 100;

            const isInView = rect.top < (viewportCenter - buffer) &&
                rect.bottom > (viewportCenter + buffer);

            setIsVisible(isInView);
        };

        window.addEventListener('scroll', checkVisibility);
        checkVisibility();

        return () => window.removeEventListener('scroll', checkVisibility);
    }, [shouldShowUnlockButton]);

    return (
        <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 relative" ref={containerRef}>
            <h2 className="text-lg sm:text-xl font-semibold text-primary mb-3 sm:mb-4">
                {t('aiAnalysis.title')}
            </h2>

            <div className="relative overflow-hidden">
                {isLocked ? (
                    <div className="prose prose-sm sm:prose lg:prose-lg prose-blue max-w-none opacity-25 pointer-events-none select-none blur">
                        <ReactMarkdown>
                            {streamContent}
                        </ReactMarkdown>
                    </div>
                ) : (
                    <div className="prose prose-sm sm:prose lg:prose-lg prose-blue max-w-none">
                        <div className={isGenerating ? 'opacity-25 pointer-events-none select-none blur' : ''}>
                            <ReactMarkdown>
                                {streamContent}
                            </ReactMarkdown>
                        </div>
                        {isGenerating && (
                            <span className="inline-block ml-1 animate-pulse">▊</span>
                        )}
                    </div>
                )}

                {shouldShowUnlockButton && isVisible && (
                    <div className="fixed left-1/2 top-[50vh] -translate-x-1/2 -translate-y-1/2 z-10 w-[calc(100%-2rem)] sm:w-full max-w-[280px] sm:max-w-md">
                        <div className="flex flex-col items-center justify-center bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
                            <TestimonialCarousel />
                            <motion.button
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5 }}
                                onClick={scrollToPricing}
                                className="flex items-center justify-center gap-2 w-full sm:w-auto min-w-[200px] px-4 sm:px-6 py-2.5 sm:py-3 bg-blue-600 text-white text-sm sm:text-base font-medium rounded-full shadow-lg hover:bg-blue-700 transition-all hover:shadow-xl"
                            >
                                <Lock className="h-4 w-4 flex-shrink-0" />
                                <span className="truncate">Unlock Full Report</span>
                            </motion.button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}; 