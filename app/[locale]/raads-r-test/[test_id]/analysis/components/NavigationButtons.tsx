'use client'

import React from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export const NavigationButtons = () => {
    const t = useTranslations('pages.raadsrAnalysis');
    const router = useRouter();

    return (
        <>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mt-6">
                <button
                    onClick={() => router.push('/raads-r-test')}
                    className="w-full sm:w-auto px-4 sm:px-6 py-3 border border-purple-600 
                             text-purple-600 text-sm sm:text-base font-medium
                             hover:bg-purple-50 rounded-lg transition-colors"
                >
                    {t('takeTestAgain')}
                </button>
            </div>

            <div className="bg-purple-50/50 border border-purple-100 p-4 rounded-lg">
                <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                    {t('disclaimer')}
                </p>
            </div>
        </>
    );
}; 