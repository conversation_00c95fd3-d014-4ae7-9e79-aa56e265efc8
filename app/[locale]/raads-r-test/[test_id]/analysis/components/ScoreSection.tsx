'use client'

import React, { useEffect, useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import { ScoreInterpretation } from './ScoreInterpretation';
import { Lock } from 'lucide-react';
import { motion } from 'framer-motion';
import { TestimonialCarousel } from './TestimonialCarousel';

interface ScoreSectionProps {
    totalScore: number;
    dimensionScores: {
        socialRelatedness: number;
        circumscribedInterests: number;
        language: number;
        sensoryMotor: number;
    };
    isLocked?: boolean;
    isGenerating?: boolean;
    scrollToPricing?: () => void;
}

const getScoreLevel = (score: number, maxScore: number): 'low' | 'moderate' | 'high' => {
    const percentage = (score / maxScore) * 100;
    if (percentage < 33) return 'low';
    if (percentage < 66) return 'moderate';
    return 'high';
};

export const ScoreSection: React.FC<ScoreSectionProps> = ({
    totalScore,
    dimensionScores,
    isLocked = false,
    isGenerating = false,
    scrollToPricing
}) => {
    const t = useTranslations('pages.raadsrAnalysis');
    const shouldBlur = isLocked || isGenerating;
    const shouldShowUnlockButton = isLocked || isGenerating;
    const containerRef = useRef<HTMLDivElement>(null);
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        if (!shouldShowUnlockButton) return;

        const checkVisibility = () => {
            if (!containerRef.current) return;
            const rect = containerRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportCenter = viewportHeight / 2;
            const buffer = 100;

            const isInView = rect.top < (viewportCenter - buffer) &&
                rect.bottom > (viewportCenter + buffer);

            setIsVisible(isInView);
        };

        window.addEventListener('scroll', checkVisibility);
        checkVisibility();

        return () => window.removeEventListener('scroll', checkVisibility);
    }, [shouldShowUnlockButton]);

    return (
        <>
            <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6">
                <h2 className="text-lg sm:text-xl font-semibold text-primary mb-3 sm:mb-4">
                    {t('totalScore.title')}
                </h2>
                <div className="text-4xl sm:text-5xl font-bold text-primary mb-3 sm:mb-4">
                    {totalScore}
                </div>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed mb-4">
                    {t('totalScore.description')}
                </p>
                <ScoreInterpretation score={totalScore} />
            </div>

            <div className="space-y-4 sm:space-y-6" ref={containerRef}>
                <h2 className="text-lg sm:text-xl font-semibold text-primary">
                    {t('dimensionScores.title')}
                </h2>

                <div className="relative">
                    <div className={`grid grid-cols-1 sm:grid-cols-2 gap-4 ${shouldBlur ? 'opacity-25 pointer-events-none select-none blur' : ''}`}>
                        {Object.entries(dimensionScores).map(([dimension, score]) => {
                            const level = getScoreLevel(score, 100);
                            return (
                                <div
                                    key={dimension}
                                    className="bg-white rounded-lg shadow-lg p-4 sm:p-6"
                                >
                                    <h3 className="text-base sm:text-lg font-semibold text-primary mb-2">
                                        {t(`dimensionScores.${dimension}.title`)}
                                    </h3>
                                    <div className="text-2xl sm:text-3xl font-bold text-primary mb-2 sm:mb-3">
                                        {score}
                                    </div>
                                    <p className="text-xs sm:text-sm text-gray-600 mb-2 leading-relaxed">
                                        {t(`dimensionScores.${dimension}.description`)}
                                    </p>
                                    <p className="text-xs sm:text-sm text-gray-800 leading-relaxed">
                                        {t(`dimensionScores.${dimension}.${level}`)}
                                    </p>
                                </div>
                            );
                        })}
                    </div>

                    {shouldShowUnlockButton && isVisible && scrollToPricing && (
                        <div className="fixed left-1/2 top-[50vh] -translate-x-1/2 -translate-y-1/2 z-10 w-[calc(100%-2rem)] sm:w-full max-w-[280px] sm:max-w-md">
                            <div className="flex flex-col items-center justify-center bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
                                <TestimonialCarousel />
                                <motion.button
                                    initial={{ opacity: 0, y: -20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 }}
                                    onClick={scrollToPricing}
                                    className="flex items-center justify-center gap-2 w-full sm:w-auto min-w-[200px] px-4 sm:px-6 py-2.5 sm:py-3 bg-blue-600 text-white text-sm sm:text-base font-medium rounded-full shadow-lg hover:bg-blue-700 transition-all hover:shadow-xl"
                                >
                                    <Lock className="h-4 w-4 flex-shrink-0" />
                                    <span className="truncate">Unlock Full Report</span>
                                </motion.button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}; 