'use client'

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Testimonial {
    name: string;
    background: string;
    quote: string;
    avatar: string;
}

const testimonials: Testimonial[] = [
    {
        name: "<PERSON>",
        background: "Software Engineer",
        quote: "The analysis provided deep insights into my cognitive patterns. It helped me understand myself better.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah&backgroundColor=b6e3f4"
    },
    {
        name: "<PERSON>",
        background: "Graduate Student",
        quote: "The detailed breakdown of each dimension was incredibly helpful for my self-understanding.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Michael&backgroundColor=c0aede"
    },
    {
        name: "<PERSON>",
        background: "Healthcare Professional",
        quote: "This analysis gave me practical insights that I could discuss with my healthcare provider.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Emily&backgroundColor=ffdfbf"
    },
    {
        name: "<PERSON>",
        background: "Teacher",
        quote: "The comprehensive report helped me better understand my strengths and areas where I might need support.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=David&backgroundColor=d1d4f9"
    }
];

export const TestimonialCarousel: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) =>
                prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
            );
        }, 5000); // Change testimonial every 5 seconds

        return () => clearInterval(timer);
    }, []);

    return (
        <div className="w-full max-w-md mx-auto mb-6">
            <AnimatePresence mode="wait">
                <motion.div
                    key={currentIndex}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className="text-center px-4"
                >
                    <div className="flex flex-col items-center">
                        <div className="w-16 h-16 mb-3 rounded-full overflow-hidden bg-gray-50 ring-2 ring-white/10">
                            <img
                                src={testimonials[currentIndex].avatar}
                                alt={testimonials[currentIndex].name}
                                className="w-full h-full"
                            />
                        </div>
                        <p className="text-sm text-gray-600 italic mb-2 line-clamp-2">&ldquo;{testimonials[currentIndex].quote}&rdquo;</p>
                        <div className="text-xs text-gray-500 flex items-center gap-1 flex-wrap justify-center">
                            <span className="font-semibold whitespace-nowrap">{testimonials[currentIndex].name}</span>
                            <span className="mx-1 hidden sm:inline">·</span>
                            <span className="whitespace-nowrap">{testimonials[currentIndex].background}</span>
                        </div>
                    </div>
                </motion.div>
            </AnimatePresence>
        </div>
    );
}; 