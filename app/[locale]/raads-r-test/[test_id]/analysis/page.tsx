import React from 'react';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { AnalysisContent } from './AnalysisContent';

export async function generateMetadata(): Promise<Metadata> {
    const t = await getTranslations('pages.raadsrAnalysis.metadata');

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('ogTitle'),
            description: t('ogDescription'),
            url: '/',
            type: 'website',
            images: [
                {
                    url: '/og-image.png',
                    width: 1200,
                    height: 630,
                    alt: 'RAADS-R Test Analysis',
                },
            ],
        },
        twitter: {
            card: 'summary_large_image',
            title: t('ogTitle'),
            description: t('ogDescription'),
            images: ['/og-image.png'],
        },
    };
}

export default function AnalysisPage() {
    return <AnalysisContent />;
}
