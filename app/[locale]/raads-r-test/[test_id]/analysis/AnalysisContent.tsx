'use client'

import React, { useEffect, useState, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Header } from './components/Header';
import { AIAnalysis } from './components/AIAnalysis';
import { PricingSection } from '@/components/PricingSection';
import { ScoreSection } from './components/ScoreSection';
import { NavigationButtons } from './components/NavigationButtons';
import { useTranslations } from 'next-intl';
import { TestimonialCarousel } from './components/TestimonialCarousel';
import { useTracking } from '@/hooks/useTracking';

interface Test {
    id: string;
    type: string;
    email: string;
    scores: {
        totalScore: number;
        scores: {
            socialRelatedness: number;
            circumscribedInterests: number;
            language: number;
            sensoryMotor: number;
        };
    };
    answers: Record<string, number>;
    analysis?: {
        id: string;
        content: string;
        isLocked: boolean;
    };
}

export const AnalysisContent = () => {
    const tTest = useTranslations('components.test');
    const router = useRouter();
    const params = useParams();
    const testId = params.test_id as string;
    const pricingSectionRef = useRef<HTMLDivElement>(null);
    const { trackAction } = useTracking();

    const [test, setTest] = useState<Test | null>(null);
    const [isGenerating, setIsGenerating] = useState(false);
    const [streamContent, setStreamContent] = useState('');
    const [isSaving, setIsSaving] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const MAX_RETRIES = 3;

    useEffect(() => {
        const fetchTestData = async () => {
            try {
                const response = await fetch(`/api/test?id=${testId}`);

                if (response.ok) {
                    const data = await response.json();
                    setTest(data);
                    trackAction('view_raads_analysis', 'analysis', testId);

                    if (data.analysis?.content) {
                        setStreamContent(data.analysis.content);
                    } else {
                        generateAIReport(data.scores);
                    }
                } else {
                    throw new Error('Failed to fetch test data');
                }
            } catch (error) {
                console.error('Error fetching test data:', error);
                const errorMessage = error instanceof Error ? error.message : String(error);
                trackAction('analysis_error', 'error', 'fetch_test_data', 0, { error: errorMessage });
                router.push('/404');
            } finally {
                setIsLoading(false);
            }
        };

        if (testId) {
            fetchTestData();
        }
    }, [testId, router, trackAction]);

    useEffect(() => {
        if (test && test.analysis && !test.analysis.isLocked) {
            // Track Google Ads conversion
            if (typeof window !== 'undefined' && (window as any).gtag) {
                (window as any).gtag('event', 'conversion', {
                    'send_to': 'AW-16758172378/c4uOCLLCtOcZENrV9bY-',
                    'transaction_id': testId
                });
            }
        }
    }, [test, testId]);

    const saveAnalysis = async (content: string) => {
        if (isSaving) return false;
        if (!testId) {
            console.error('No test ID available');
            return false;
        }

        setIsSaving(true);
        try {
            const response = await fetch('/api/analysis/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content,
                    testId: testId,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save analysis');
            }

            const { analysis, test: updatedTest } = await response.json();

            setTest(prev => prev ? {
                ...prev,
                analysis: analysis
            } : updatedTest);

            return true;
        } catch (error) {
            console.error('Error saving analysis:', error);
            return false;
        } finally {
            setIsSaving(false);
        }
    };

    const generateAIReport = async (scoreData: any) => {
        if (!scoreData || isGenerating) return;

        setIsGenerating(true);
        setError(null);
        setStreamContent('');
        trackAction('generate_raads_analysis', 'analysis', testId, 1, { scores: scoreData });

        try {
            const prompt = generateRaadsPrompt(scoreData);

            const response = await fetch('/api/analysis/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt }),
            });

            if (!response.ok) {
                throw new Error(`Failed to generate analysis: ${response.statusText}`);
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            let fullContent = '';

            while (true) {
                const { done, value } = await reader!.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') break;
                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.error) {
                                throw new Error(parsed.error);
                            }
                            fullContent += parsed.content;
                            setStreamContent(fullContent);
                        } catch (e) {
                            console.error('Error parsing SSE data:', e);
                            continue;
                        }
                    }
                }
            }

            const saved = await saveAnalysis(fullContent);
            if (!saved) {
                throw new Error('Failed to save analysis');
            }
            trackAction('analysis_generated', 'analysis', testId, 1, { success: true });

        } catch (error) {
            console.error('Error generating report:', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            trackAction('analysis_error', 'error', 'generate_report', 0, { error: errorMessage });
            setError(errorMessage);

            if (retryCount < MAX_RETRIES) {
                setRetryCount(prev => prev + 1);
                setTimeout(() => {
                    generateAIReport(scoreData);
                }, 2000);
            }
        } finally {
            setIsGenerating(false);
        }
    };

    const generateRaadsPrompt = (data: any): string => {
        const detailedResponses = getDetailedQuestionsAndAnswers(data);

        return `As an expert in autism spectrum analysis with extensive clinical experience, provide a comprehensive and detailed analysis of this RAADS-R test data. Consider both the quantitative scores and qualitative response patterns:

Total Score: ${data.totalScore}
Dimension Scores:
- Social Relatedness: ${data.scores.socialRelatedness}
- Circumscribed Interests: ${data.scores.circumscribedInterests}
- Language: ${data.scores.language}
- Sensory Motor: ${data.scores.sensoryMotor}

Detailed Responses by Dimension:
${detailedResponses}

Please provide an extensive analysis following this structure:

# Overall Assessment (2-3 paragraphs)
- Provide a nuanced interpretation of the total score in context
- Compare scores with typical ranges for both neurotypical and autistic individuals
- Discuss the reliability and validity of the response pattern
- Analyze the internal consistency of responses
- Highlight any unique or notable aspects of this profile

# Detailed Dimension Analysis

## Social Relatedness Analysis (Score: ${data.scores.socialRelatedness})
- Analyze specific social interaction patterns and their real-world implications
- Identify both challenges and strengths in social communication
- Discuss impact on relationships (personal, professional, family)
- Examine social anxiety vs. social interest patterns
- Suggest specific social support strategies

## Circumscribed Interests Analysis (Score: ${data.scores.circumscribedInterests})
- Evaluate the nature and intensity of interests
- Analyze how interests impact daily functioning
- Discuss potential benefits and challenges of interest patterns
- Explore ways to leverage interests positively
- Consider executive functioning implications

## Language Processing Analysis (Score: ${data.scores.language})
- Analyze pragmatic language use and understanding
- Evaluate literal vs. figurative language comprehension
- Identify specific communication strengths and challenges
- Discuss impact on social and professional communication
- Suggest practical communication strategies

## Sensory Motor Analysis (Score: ${data.scores.sensoryMotor})
- Detail specific sensory sensitivities and their impact
- Analyze motor coordination and its effects on daily life
- Identify environmental triggers and challenges
- Discuss sensory seeking vs. avoidance patterns
- Recommend specific accommodations

# Pattern Analysis
- Identify correlations between different dimensions
- Analyze response consistency across related questions
- Highlight unique or unexpected response patterns
- Discuss how different traits interact and influence each other
- Consider masking or compensation strategies indicated

# Comprehensive Recommendations

## Professional Support Recommendations
- Suggest specific types of professional evaluations
- Recommend relevant specialists based on profile
- Prioritize areas for professional attention
- Discuss potential therapeutic approaches
- Consider co-occurring condition screenings

## Daily Life Strategies
- Provide detailed coping mechanisms for identified challenges
- Suggest specific environmental modifications
- Recommend social support strategies
- Outline communication techniques
- Discuss stress management approaches

## Personal Development Plan
- Identify areas for skill development
- Suggest ways to leverage identified strengths
- Provide self-advocacy strategies
- Recommend educational and occupational approaches
- Discuss identity and self-understanding

## Support Network Recommendations
- Suggest family education resources
- Recommend community support options
- Discuss peer support opportunities
- Outline advocacy group connections
- Consider cultural and contextual factors

# Future Considerations
- Suggest areas for ongoing monitoring
- Recommend follow-up assessments
- Discuss developmental considerations
- Consider long-term support needs
- Outline preventive strategies

Remember to:
- Provide specific examples from the test responses
- Maintain a strengths-based, respectful perspective
- Include practical, actionable insights
- Consider cultural and contextual factors
- Acknowledge individual uniqueness
- Note this is a screening tool, not a diagnostic instrument
- Emphasize the importance of professional evaluation

Please write in a clear, professional yet accessible style, providing detailed explanations and specific examples throughout. Focus on practical implications and actionable insights while maintaining scientific accuracy.`;
    };

    const getDetailedQuestionsAndAnswers = (data: any) => {
        const answers = typeof data.answers === 'string'
            ? JSON.parse(data.answers)
            : (data.answers || {});

        const questions = Array.from({ length: 80 }, (_, i) => {
            const questionNumber = i + 1;
            const answerIndex = answers[questionNumber] ||
                answers[`${questionNumber}`] ||
                answers[i] || 0;

            return {
                id: questionNumber,
                text: tTest(`questions.${questionNumber}`),
                answer: answerIndex,
                answerText: tTest(`options.${[
                    'neverTrue',
                    'trueOnlyBefore',
                    'trueSometimes',
                    'trueNow'
                ][answerIndex]}`)
            };
        });

        const dimensionQuestions = {
            socialRelatedness: [1, 6, 8, 11, 14, 17, 18, 25, 37, 38, 3, 5, 12, 28, 39, 44, 45, 76, 79, 80, 20, 21, 22, 23, 26, 31, 43, 47, 48, 53, 54, 55, 60, 61, 64, 68, 69, 72, 77],
            circumscribedInterests: [9, 13, 24, 30, 32, 40, 41, 50, 52, 56, 63, 70, 75, 78],
            language: [2, 7, 27, 35, 58, 66, 15],
            sensoryMotor: [10, 19, 4, 33, 34, 36, 46, 71, 16, 29, 42, 49, 51, 57, 59, 62, 65, 67, 73, 74]
        };

        const dimensionResponses = Object.entries(dimensionQuestions).map(([dimension, questionIds]) => {
            const dimensionQs = questionIds.map(id => {
                const q = questions[id - 1];
                return `Q${q.id}: "${q.text}"\nResponse: ${q.answerText}`;
            }).join('\n\n');

            return `## ${dimension.charAt(0).toUpperCase() + dimension.slice(1)} Dimension Questions:\n${dimensionQs}`;
        }).join('\n\n');

        return dimensionResponses;
    };

    const scrollToPricing = () => {
        trackAction('view_pricing', 'pricing', 'raads_analysis', 1);
        pricingSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    };

    if (isLoading) {
        return (
            <div className="min-h-[50vh] flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (!test) {
        return null;
    }

    return (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6 sm:space-y-8"
            >
                <Header />

                <AIAnalysis
                    isLocked={test.analysis?.isLocked || false}
                    streamContent={streamContent}
                    isGenerating={isGenerating}
                    scrollToPricing={scrollToPricing}
                />

                {(test.analysis?.isLocked || isGenerating) && (
                    <>
                        <TestimonialCarousel />
                        <PricingSection
                            isLocked={test.analysis?.isLocked || isGenerating}
                            pricingSectionRef={pricingSectionRef}
                            testType="raadsr"
                            isGenerating={isGenerating}
                        />
                    </>
                )}

                <ScoreSection
                    totalScore={test.scores.totalScore}
                    dimensionScores={test.scores.scores}
                    isLocked={test.analysis?.isLocked}
                    isGenerating={isGenerating}
                    scrollToPricing={scrollToPricing}
                />

                <NavigationButtons />
            </motion.div>
        </div>
    );
}; 