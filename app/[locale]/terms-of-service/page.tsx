import { useTranslations } from 'next-intl';
import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Terms of Service | Always Health',
    description: 'Review Always Health\'s terms of service, user guidelines, and legal information for using our mental health assessment platform.',
};

export default function TermsOfService() {
    const t = useTranslations('pages.TermsOfService');

    return (
        <div className="max-w-4xl mx-auto px-4">
            <main className="py-8 md:py-12">
                <div className="space-y-8">
                    <div className="text-center mb-12">
                        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-text-100">
                            {t('title')}
                        </h1>
                        <p className="text-lg md:text-xl text-text-200">
                            {t('description')}
                        </p>
                    </div>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section1')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section1Para1')}</p>
                            <p className="text-text-200">{t('section1Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section2')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section2Para1')}</p>
                            <p className="text-text-200">{t('section2Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section3')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section3Para1')}</p>
                            <p className="text-text-200">{t('section3Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section4')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section4Para1')}</p>
                            <p className="text-text-200">{t('section4Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm border-l-4 border-accent-100">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section5')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section5Para1')}</p>
                            <p className="text-text-200">{t('section5Para2')}</p>
                            <p className="text-text-200">{t('section5Para3')}</p>
                        </div>
                    </section>

                    <p className="text-sm text-text-200 text-center pt-4">
                        {t('lastUpdated')}
                    </p>
                </div>
            </main>
        </div>
    );
}