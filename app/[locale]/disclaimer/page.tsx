import { useTranslations } from 'next-intl';
import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Disclaimer | Autism Test Free',
    description: 'Important disclaimer information for users of Autism Test Free, including details about our assessment tools, limitations, and appropriate use.',
};

export default function Disclaimer() {
    const t = useTranslations('pages.Disclaimer');
    const footerT = useTranslations('components.footer');

    return (
        <div className="max-w-4xl mx-auto px-4">
            <main className="py-8 md:py-12">
                <div className="space-y-8">
                    <div className="text-center mb-12">
                        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-text-100">
                            {t('title')}
                        </h1>
                        <p className="text-lg md:text-xl text-text-200">
                            {t('description')}
                        </p>
                    </div>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section1')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section1Para1')}</p>
                            <p className="text-text-200">{t('section1Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section2')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section2Para1')}</p>
                            <p className="text-text-200">{t('section2Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section3')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section3Para1')}</p>
                            <p className="text-text-200">{t('section3Para2')}</p>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <h2 className="text-xl md:text-2xl font-semibold mb-4 text-text-100">
                            {t('section4')}
                        </h2>
                        <div className="space-y-4">
                            <p className="text-text-200">{t('section4Para1')}</p>
                            <p className="text-text-200">{t('section4Para2')}</p>
                            <div className="mt-6 text-sm text-text-200/80 border-t border-gray-200 pt-4">
                                <p className="mb-1">
                                    {footerT('contact.email')}: <a href="mailto:<EMAIL>" className="text-primary-100 hover:underline"><EMAIL></a>
                                </p>
                                <p className="italic">
                                    {footerT('payment.processing')}
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="bg-bg-100 rounded-lg p-6 md:p-8 shadow-sm">
                        <p className="text-sm text-text-200 italic text-center">
                            {t('lastUpdated')}
                        </p>
                    </section>
                </div>
            </main>
        </div>
    );
} 