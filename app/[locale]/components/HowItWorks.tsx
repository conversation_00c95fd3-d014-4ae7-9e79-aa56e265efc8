import { useTranslations } from 'next-intl';

interface Step {
    number: number;
}

const steps: Step[] = [
    { number: 1 },
    { number: 2 },
    { number: 3 }
];

export const HowItWorks = () => {
    const t = useTranslations('pages.home.howItWorks');

    return (
        <section className="mb-16 bg-bg-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-text-100 mb-8">{t('title')}</h2>
            <div className="grid gap-6 sm:grid-cols-3">
                {steps.map((step) => (
                    <div key={step.number} className="flex flex-col items-center text-center p-4">
                        <div className="w-12 h-12 bg-primary-100/20 rounded-full flex items-center justify-center text-primary-100 mb-4">
                            {step.number}
                        </div>
                        <h3 className="font-semibold mb-2">{t(`steps.${step.number - 1}.title`)}</h3>
                        <p className="text-text-200 text-sm">{t(`steps.${step.number - 1}.description`)}</p>
                    </div>
                ))}
            </div>
        </section>
    );
}; 