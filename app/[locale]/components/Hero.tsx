import Image from 'next/image';
import { useTranslations } from 'next-intl';

export const Hero = () => {
    const t = useTranslations('pages.home.hero');

    return (
        <section className="mb-16">
            <div className="flex flex-col gap-6">
                <div className="flex items-start gap-4 sm:gap-6">
                    <div className="relative shrink-0 self-stretch">
                        <div className="relative w-20 sm:w-24 md:w-28 lg:w-32 h-[calc(100%-4px)]">
                            <Image
                                src="/logo.png"
                                alt={t('logoAlt')}
                                fill
                                className="object-contain object-center"
                                priority
                            />
                        </div>
                    </div>
                    <div className="flex-1">
                        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl leading-tight">
                            <span className="block mb-2 text-lg sm:text-xl md:text-2xl font-medium text-primary-100">
                                {t('subtitle')}
                            </span>
                            <span className="block font-bold text-text-100">
                                {t('title')}
                            </span>
                        </h1>
                        <p className="mt-4 text-text-200 text-sm sm:text-base">
                            {t('description')}
                        </p>
                    </div>
                </div>
            </div>
        </section>
    );
}; 