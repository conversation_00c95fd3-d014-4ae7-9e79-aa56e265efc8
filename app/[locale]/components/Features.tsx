import { useTranslations } from 'next-intl';

interface Feature {
    index: number;
}

const features: Feature[] = [
    { index: 0 },
    { index: 1 },
    { index: 2 },
    { index: 3 }
];

export const Features = () => {
    const t = useTranslations('pages.home.features');

    return (
        <section className="mb-16 bg-bg-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-text-100 mb-8">{t('title')}</h2>
            <div className="grid gap-6 sm:grid-cols-2">
                {features.map((feature) => (
                    <div key={feature.index} className="p-4 border border-bg-300/50 rounded-lg bg-white">
                        <h3 className="text-lg font-bold text-primary-100 mb-3">
                            {t(`items.${feature.index}.title`)}
                        </h3>
                        <p className="text-text-200 text-sm">
                            {t(`items.${feature.index}.description`)}
                        </p>
                    </div>
                ))}
            </div>
        </section>
    );
}; 