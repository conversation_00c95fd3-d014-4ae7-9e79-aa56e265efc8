'use client';

import { Link } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';

interface TestCard {
    href: '/raads-r-test' | '/adhd-test';
    key: string;
}

const tests: TestCard[] = [
    {
        href: '/raads-r-test',
        key: 'raadsr'
    },
    {
        href: '/adhd-test',
        key: 'adhd'
    }
];

export default function PopularTests() {
    const t = useTranslations('pages.home.popularTests');

    return (
        <section className="mb-16">
            <h2 className="text-2xl font-bold text-text-100 mb-6">{t('title')}</h2>
            <div className="grid gap-4 sm:gap-6 sm:grid-cols-2">
                {tests.map((test) => (
                    <Link
                        key={test.href}
                        href={test.href}
                        className="group relative bg-primary-100/5 hover:bg-primary-100/10 rounded-xl p-5 sm:p-6 transition-all duration-300 border-2 border-primary-100 shadow-sm hover:shadow-lg flex flex-col overflow-hidden"
                    >
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5 }}
                            className="flex-grow"
                        >
                            <h3 className="text-xl sm:text-2xl font-bold text-primary-100 mb-3">
                                {t(`tests.${test.key}.title`)}
                            </h3>
                            <p className="text-text-200 text-base sm:text-lg mb-6">
                                {t(`tests.${test.key}.description`)}
                            </p>
                        </motion.div>
                        <div className="mt-auto">
                            <motion.button
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className="w-full bg-primary-100 text-white py-3 sm:py-4 px-6 rounded-lg font-semibold text-base sm:text-lg hover:bg-primary-200 transition-all duration-300 flex items-center justify-center gap-2 group-hover:shadow-md"
                            >
                                <span>{t('startTest')}</span>
                                <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                            </motion.button>
                        </div>
                        <div className="absolute inset-0 bg-gradient-to-br from-primary-100/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                    </Link>
                ))}
            </div>
        </section>
    );
} 