import { useTranslations } from 'next-intl';

export function UpcomingTests() {
    const t = useTranslations('pages.home.sections.upcomingTests');
    const tests = t.raw('tests') as Array<{ name: string; description: string }>;

    return (
        <section className="mb-16 bg-bg-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-text-100 mb-8 text-center">{t('title')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {tests.map((test, index) => (
                    <div
                        key={index}
                        className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
                    >
                        <h3 className="font-semibold text-primary-100 mb-2">{test.name}</h3>
                        <p className="text-text-200 text-sm">{test.description}</p>
                    </div>
                ))}
            </div>
        </section>
    );
} 