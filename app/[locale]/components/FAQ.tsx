import { useTranslations } from 'next-intl';

interface FAQItem {
    index: number;
}

const faqs: FAQItem[] = [
    { index: 0 },
    { index: 1 },
    { index: 2 },
    { index: 3 },
    { index: 4 },
    { index: 5 },
    { index: 6 }
];

export const FAQ = () => {
    const t = useTranslations('pages.home.faq');

    return (
        <section className="mb-16 bg-bg-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-text-100 mb-8">{t('title')}</h2>
            <div className="space-y-4">
                {faqs.map((faq) => (
                    <div key={faq.index} className="bg-white rounded-lg p-6 border border-bg-300">
                        <h3 className="text-lg font-bold text-primary-100 mb-2">
                            {t(`items.${faq.index}.question`)}
                        </h3>
                        <p className="text-text-200 text-sm">
                            {t(`items.${faq.index}.answer`)}
                        </p>
                    </div>
                ))}
            </div>
        </section>
    );
}; 