import { useTranslations } from 'next-intl';

export function RelatedTests() {
    const t = useTranslations('pages.home.sections.relatedTests');
    const tests = [
        { key: 'raadsR' },
        { key: 'itc', link: 'https://knowledge.autismtestfree.com/infant-toddler-checklistitc/' },
        { key: 'qChat', link: 'https://knowledge.autismtestfree.com/quantitative-checklist-for-autism-in-toddlersq-chat/' },
        { key: 'catQ', link: 'https://knowledge.autismtestfree.com/the-camouflaging-autistic-traits-questionnaire-cat-q/' }
    ];

    return (
        <section className="bg-bg-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-text-100 mb-8 text-center">{t('title')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {tests.map(({ key, link }) => (
                    <div key={key} className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                        <h3 className="font-semibold mb-2">
                            {link ? (
                                <a href={link} className="text-primary-100 hover:underline">
                                    {t(`tests.${key}.title`)}
                                </a>
                            ) : (
                                <span className="text-primary-100">
                                    {t(`tests.${key}.title`)}
                                </span>
                            )}
                        </h3>
                        <p className="text-text-200 text-sm">
                            {t(`tests.${key}.description`)}
                        </p>
                    </div>
                ))}
            </div>
        </section>
    );
}
