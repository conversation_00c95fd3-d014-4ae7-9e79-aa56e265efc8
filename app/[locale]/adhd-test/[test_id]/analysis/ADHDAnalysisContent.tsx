'use client'

import React, { useEffect, useState, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Header } from './components/Header';
import { AIAnalysis } from './components/AIAnalysis';
import { PricingSection } from '@/components/PricingSection';
import { ScoreSection } from './components/ScoreSection';
import { NavigationButtons } from './components/NavigationButtons';
import { useTranslations } from 'next-intl';
import { TestimonialsCarousel } from './components/TestimonialsCarousel';
import { useTracking } from '@/hooks/useTracking';

interface ADHDScores {
    inattentiveScore: number;
    hyperactiveImpulsiveScore: number;
    combinedScore: number;
    performanceIssues: number;
    subtype: 'inattentive' | 'hyperactive' | 'combined' | 'none';
}

interface Test {
    id: string;
    type: string;
    email: string;
    scores: ADHDScores;
    answers: Record<string, number>;
    analysis?: {
        id: string;
        content: string;
        isLocked: boolean;
    };
}

export const ADHDAnalysisContent = () => {
    const tTest = useTranslations('components.adhd-test');
    const router = useRouter();
    const params = useParams();
    const testId = params.test_id as string;
    const pricingSectionRef = useRef<HTMLDivElement>(null);
    const { trackAction } = useTracking();

    const [test, setTest] = useState<Test | null>(null);
    const [isGenerating, setIsGenerating] = useState(false);
    const [streamContent, setStreamContent] = useState('');
    const [isSaving, setIsSaving] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const MAX_RETRIES = 3;

    useEffect(() => {
        const fetchTestData = async () => {
            try {
                const response = await fetch(`/api/test?id=${testId}`);

                if (response.ok) {
                    const data = await response.json();
                    setTest(data);
                    trackAction('view_adhd_analysis', 'analysis', testId);

                    if (data.analysis?.content) {
                        setStreamContent(data.analysis.content);
                    } else {
                        generateAIReport(data.scores);
                    }
                } else {
                    throw new Error('Failed to fetch test data');
                }
            } catch (error) {
                console.error('Error fetching test data:', error);
                const errorMessage = error instanceof Error ? error.message : String(error);
                trackAction('analysis_error', 'error', 'fetch_test_data', 0, { error: errorMessage });
                router.push('/404');
            } finally {
                setIsLoading(false);
            }
        };

        if (testId) {
            fetchTestData();
        }
    }, [testId, router, trackAction]);

    useEffect(() => {
        if (test && test.analysis && !test.analysis.isLocked) {
            // Track Google Ads conversion
            if (typeof window !== 'undefined' && (window as any).gtag) {
                (window as any).gtag('event', 'conversion', {
                    'send_to': 'AW-16758172378/c4uOCLLCtOcZENrV9bY-',
                    'transaction_id': testId
                });
            }
        }
    }, [test, testId]);

    const saveAnalysis = async (content: string) => {
        if (isSaving) return false;
        if (!testId) {
            console.error('No test ID available');
            return false;
        }

        setIsSaving(true);
        try {
            const response = await fetch('/api/analysis/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content,
                    testId: testId,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save analysis');
            }

            const { analysis, test: updatedTest } = await response.json();

            setTest(prev => prev ? {
                ...prev,
                analysis: analysis
            } : updatedTest);

            return true;
        } catch (error) {
            console.error('Error saving analysis:', error);
            return false;
        } finally {
            setIsSaving(false);
        }
    };

    const generateAIReport = async (scoreData: ADHDScores) => {
        if (!scoreData || isGenerating) return;

        setIsGenerating(true);
        setError(null);
        setStreamContent('');
        trackAction('generate_adhd_analysis', 'analysis', testId, 1, { scores: scoreData });

        try {
            const prompt = generateADHDPrompt(scoreData);

            const response = await fetch('/api/analysis/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt }),
            });

            if (!response.ok) {
                throw new Error(`Failed to generate analysis: ${response.statusText}`);
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            let fullContent = '';

            while (true) {
                const { done, value } = await reader!.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') break;
                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.error) {
                                throw new Error(parsed.error);
                            }
                            fullContent += parsed.content;
                            setStreamContent(fullContent);
                        } catch (e) {
                            console.error('Error parsing SSE data:', e);
                            continue;
                        }
                    }
                }
            }

            const saved = await saveAnalysis(fullContent);
            if (!saved) {
                throw new Error('Failed to save analysis');
            }
            trackAction('analysis_generated', 'analysis', testId, 1, { success: true });

        } catch (error) {
            console.error('Error generating report:', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            trackAction('analysis_error', 'error', 'generate_report', 0, { error: errorMessage });
            setError(errorMessage);

            if (retryCount < MAX_RETRIES) {
                setRetryCount(prev => prev + 1);
                setTimeout(() => {
                    generateAIReport(scoreData);
                }, 2000);
            }
        } finally {
            setIsGenerating(false);
        }
    };

    const generateADHDPrompt = (data: ADHDScores): string => {
        return `As an expert in ADHD assessment with extensive clinical experience, provide a comprehensive and detailed analysis of this ADHD test data. Consider both the quantitative scores and their clinical implications:

Scores:
- Inattentive Score: ${data.inattentiveScore}
- Hyperactive-Impulsive Score: ${data.hyperactiveImpulsiveScore}
- Combined Score: ${data.combinedScore}
- Performance Issues Score: ${data.performanceIssues}
- Identified Subtype: ${data.subtype}

Please provide an extensive analysis following this structure:

# Overall Assessment (2-3 paragraphs)
- Provide a nuanced interpretation of the scores in context
- Discuss the presentation type based on the scores
- Analyze the severity and impact of symptoms
- Consider the reliability of the response pattern
- Highlight any unique aspects of this profile

# Detailed Symptom Analysis

## Inattention Analysis (Score: ${data.inattentiveScore})
- Analyze specific attention-related challenges
- Identify impact on daily functioning
- Discuss executive functioning implications
- Examine focus and concentration patterns
- Suggest specific coping strategies

## Hyperactivity-Impulsivity Analysis (Score: ${data.hyperactiveImpulsiveScore})
- Evaluate the nature and intensity of hyperactive symptoms
- Analyze impulsivity patterns and their impact
- Discuss behavioral manifestations
- Consider emotional regulation aspects
- Recommend management strategies

## Performance Impact Analysis (Score: ${data.performanceIssues})
- Detail impact on academic/professional performance
- Analyze organizational challenges
- Identify time management issues
- Discuss productivity patterns
- Suggest accommodations and strategies

# Comprehensive Recommendations

## Professional Support Recommendations
- Suggest types of professional evaluations needed
- Recommend relevant specialists
- Discuss potential treatment approaches
- Consider medication evaluation needs
- Address co-occurring condition possibilities

## Daily Life Strategies
- Provide specific coping mechanisms
- Suggest environmental modifications
- Recommend organizational tools
- Outline time management techniques
- Discuss stress management approaches

## Support System Recommendations
- Suggest family/partner education resources
- Recommend workplace/school accommodations
- Discuss support group opportunities
- Outline advocacy strategies
- Consider relationship impact management

# Future Considerations
- Suggest monitoring and follow-up plans
- Recommend ongoing assessment needs
- Discuss long-term management strategies
- Consider life transition planning
- Outline preventive approaches

Remember to:
- Provide practical, actionable insights
- Maintain a strengths-based perspective
- Consider age-appropriate interventions
- Acknowledge individual differences
- Note this is a screening tool, not diagnostic
- Emphasize the importance of professional evaluation

Please write in a clear, professional yet accessible style, providing detailed explanations and specific examples throughout. Focus on practical implications while maintaining scientific accuracy.`;
    };

    const scrollToPricing = () => {
        trackAction('view_pricing', 'pricing', 'adhd_analysis', 1);
        pricingSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    };

    if (isLoading) {
        return (
            <div className="min-h-[50vh] flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (!test) {
        return null;
    }

    return (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6 sm:space-y-8"
            >
                <Header />

                <AIAnalysis
                    isLocked={test.analysis?.isLocked || false}
                    streamContent={streamContent}
                    isGenerating={isGenerating}
                    scrollToPricing={scrollToPricing}
                />

                {(test.analysis?.isLocked || isGenerating) && (
                    <>
                        <TestimonialsCarousel />
                        <PricingSection
                            isLocked={test.analysis?.isLocked || isGenerating}
                            pricingSectionRef={pricingSectionRef}
                            testType="adhd"
                            isGenerating={isGenerating}
                        />
                    </>
                )}

                <ScoreSection
                    scores={test.scores}
                    isLocked={test.analysis?.isLocked ?? false}
                    isGenerating={isGenerating}
                    scrollToPricing={scrollToPricing}
                />

                <NavigationButtons />
            </motion.div>
        </div>
    );
}; 