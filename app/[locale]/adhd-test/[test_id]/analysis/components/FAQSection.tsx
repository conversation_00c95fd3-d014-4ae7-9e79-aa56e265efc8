'use client'

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface FAQItemProps {
    question: string;
    answer: string;
    isOpen: boolean;
    onToggle: () => void;
}

const FAQItem = ({ question, answer, isOpen, onToggle }: FAQItemProps) => {
    return (
        <div className="py-2">
            <button
                onClick={onToggle}
                className="flex justify-between items-center w-full py-4 text-left group"
            >
                <span className="font-medium text-primary text-sm sm:text-base pr-8">{question}</span>
                <ChevronDown
                    className={`w-5 h-5 text-gray-500 transition-transform flex-shrink-0 ${isOpen ? 'transform rotate-180' : ''
                        }`}
                />
            </button>
            <AnimatePresence>
                {isOpen && (
                    <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="overflow-hidden"
                    >
                        <div className="pb-4 text-gray-600 text-sm sm:text-base leading-relaxed whitespace-pre-wrap">
                            {answer}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export const FAQSection = () => {
    const t = useTranslations('components.faq.adhd');
    const [openIndex, setOpenIndex] = useState<number | null>(0);

    const faqs = [
        { question: t('test.question'), answer: t('test.answer') },
        { question: t('accuracy.question'), answer: t('accuracy.answer') },
        { question: t('scoring.question'), answer: t('scoring.answer') },
        { question: t('subtypes.question'), answer: t('subtypes.answer') },
        { question: t('symptoms.question'), answer: t('symptoms.answer') },
        { question: t('performance.question'), answer: t('performance.answer') },
        { question: t('diagnosis.question'), answer: t('diagnosis.answer') },
        { question: t('differences.question'), answer: t('differences.answer') },
        { question: t('treatment.question'), answer: t('treatment.answer') },
        { question: t('monitoring.question'), answer: t('monitoring.answer') }
    ];

    return (
        <div className="w-full max-w-3xl mx-auto px-4 sm:px-6">
            <div className="text-center mb-8">
                <h2 className="text-2xl sm:text-3xl font-semibold text-primary">
                    {t('title')}
                </h2>
                <p className="text-gray-600 text-sm sm:text-base mt-2">
                    {t('subtitle')}
                </p>
            </div>

            <div className="divide-y divide-gray-200">
                {faqs.map((faq, index) => (
                    <FAQItem
                        key={index}
                        question={faq.question}
                        answer={faq.answer}
                        isOpen={openIndex === index}
                        onToggle={() => setOpenIndex(openIndex === index ? null : index)}
                    />
                ))}
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
                <p className="text-sm text-gray-500 text-center">
                    {t('disclaimer')}
                </p>
            </div>
        </div>
    );
}; 