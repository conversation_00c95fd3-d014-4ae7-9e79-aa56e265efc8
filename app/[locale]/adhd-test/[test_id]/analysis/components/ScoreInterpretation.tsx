'use client'

import { useTranslations } from 'next-intl';

interface ScoreInterpretationProps {
    inattentiveScore: number;
    hyperactiveScore: number;
}

export const ScoreInterpretation = ({ inattentiveScore, hyperactiveScore }: ScoreInterpretationProps) => {
    const t = useTranslations('components.scoreInterpretation.adhd');

    const getScoreCategory = (score: number) => {
        if (score <= 2) return { range: 'scores.0', color: 'green' };
        if (score <= 4) return { range: 'scores.1', color: 'blue' };
        if (score <= 6) return { range: 'scores.2', color: 'yellow' };
        if (score <= 8) return { range: 'scores.3', color: 'orange' };
        return { range: 'scores.4', color: 'red' };
    };

    const getColorClass = (color: string) => {
        const colorMap: { [key: string]: string } = {
            green: "border-green-500",
            blue: "border-blue-500",
            yellow: "border-yellow-500",
            orange: "border-orange-500",
            red: "border-red-500"
        };
        return colorMap[color] || "border-gray-500";
    };

    const inattentiveCategory = getScoreCategory(inattentiveScore);
    const hyperactiveCategory = getScoreCategory(hyperactiveScore);

    return (
        <div className="mt-6 space-y-4">
            <div className="flex flex-col gap-2">
                <h3 className="text-lg font-semibold text-primary">
                    {t('title')}
                </h3>
                <p className="text-gray-600 text-sm">
                    {t('subtitle')}
                </p>
            </div>

            <div className="space-y-3">
                <div
                    className={`bg-primary/[0.02] rounded-lg p-4 hover:bg-primary/[0.04] 
                               transition-colors duration-200 border-l-4 
                               ${getColorClass(inattentiveCategory.color)}`}
                >
                    <h4 className="font-medium text-base text-primary mb-2">
                        {t('interpretation.inattentive.title')}
                    </h4>
                    <p className="text-primary/70 text-sm leading-relaxed">
                        {t('interpretation.inattentive.description')}
                    </p>
                </div>

                <div
                    className={`bg-primary/[0.02] rounded-lg p-4 hover:bg-primary/[0.04] 
                               transition-colors duration-200 border-l-4 
                               ${getColorClass(hyperactiveCategory.color)}`}
                >
                    <h4 className="font-medium text-base text-primary mb-2">
                        {t('interpretation.hyperactive.title')}
                    </h4>
                    <p className="text-primary/70 text-sm leading-relaxed">
                        {t('interpretation.hyperactive.description')}
                    </p>
                </div>
            </div>
        </div>
    );
}; 