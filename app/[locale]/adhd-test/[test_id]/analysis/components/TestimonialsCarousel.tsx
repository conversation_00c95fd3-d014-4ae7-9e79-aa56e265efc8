'use client'

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Testimonial {
    name: string;
    background: string;
    quote: string;
    avatar: string;
}

const testimonials: Testimonial[] = [
    {
        name: "<PERSON>",
        background: "Creative Professional",
        quote: "The ADHD analysis helped me understand my unique way of thinking and working. It's been transformative for my career.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alex&backgroundColor=b6e3f4"
    },
    {
        name: "<PERSON>",
        background: "College Student",
        quote: "Finally, I have clear insights into my attention patterns. The detailed report helped me develop better study strategies.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Maria&backgroundColor=c0aede"
    },
    {
        name: "<PERSON>",
        background: "Business Owner",
        quote: "Understanding my ADHD profile has helped me build better systems at work and improve my team communication.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=James&backgroundColor=ffdfbf"
    },
    {
        name: "<PERSON>",
        background: "Parent & Professional",
        quote: "The analysis gave me practical insights about managing both work and family life with ADHD. It's been eye-opening.",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Lisa&backgroundColor=d1d4f9"
    }
];

export const TestimonialsCarousel: React.FC = () => {
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) =>
                prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
            );
        }, 5000); // Change testimonial every 5 seconds

        return () => clearInterval(timer);
    }, []);

    return (
        <div className="w-full max-w-md mx-auto mb-6">
            <AnimatePresence mode="wait">
                <motion.div
                    key={currentIndex}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className="text-center px-4"
                >
                    <div className="flex flex-col items-center">
                        <div className="w-16 h-16 mb-3 rounded-full overflow-hidden bg-gray-50 ring-2 ring-white/10">
                            <img
                                src={testimonials[currentIndex].avatar}
                                alt={testimonials[currentIndex].name}
                                className="w-full h-full"
                            />
                        </div>
                        <p className="text-sm text-gray-600 italic mb-2 line-clamp-2">&ldquo;{testimonials[currentIndex].quote}&rdquo;</p>
                        <div className="text-xs text-gray-500 flex items-center gap-1 flex-wrap justify-center">
                            <span className="font-semibold whitespace-nowrap">{testimonials[currentIndex].name}</span>
                            <span className="mx-1 hidden sm:inline">·</span>
                            <span className="whitespace-nowrap">{testimonials[currentIndex].background}</span>
                        </div>
                    </div>
                </motion.div>
            </AnimatePresence>
        </div>
    );
}; 