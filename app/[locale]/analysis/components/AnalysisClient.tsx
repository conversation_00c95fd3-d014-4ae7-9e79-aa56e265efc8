'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { motion } from 'framer-motion'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search, Mail, AlertCircle, Lock, Calendar, FileText, Sparkles, Loader2 } from 'lucide-react'
import { cn } from '@/helpers/utils'
import TestCard from '@/components/TestCard'
import { TestType } from '@prisma/client'
import { useRouter } from '@/i18n/routing'

interface SearchResult {
    id: string;
    type: TestType;
    email: string;
    scores: any;
    createdAt: string;
    analysis: {
        id: string;
        content: string;
        isLocked: boolean;
        payment?: {
            status: string;
        };
    } | null;
}

export default function AnalysisClient() {
    const t = useTranslations('pages.analysis')
    const [email, setEmail] = useState('')
    const [isSearching, setIsSearching] = useState(false)
    const [results, setResults] = useState<SearchResult[]>([])
    const [hasSearched, setHasSearched] = useState(false)
    const [isLoadingPortal, setIsLoadingPortal] = useState(false)
    const router = useRouter()

    const handleSearch = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSearching(true)

        try {
            const response = await fetch(`/api/analysis/search?email=${encodeURIComponent(email)}`)
            const data = await response.json()
            setResults(data)
            setHasSearched(true)
        } catch (error) {
            console.error('Search failed:', error)
        } finally {
            setIsSearching(false)
        }
    }

    const handleNavigateToAnalysis = (testId: string, testType: TestType) => {
        const pathname = testType === 'RAADS'
            ? '/raads-r-test/[test_id]/analysis'
            : '/adhd-test/[test_id]/analysis';
        router.push({ pathname: pathname as any, params: { test_id: testId } });
    };

    const handlePortalAccess = async () => {
        if (!email || isLoadingPortal) return;

        setIsLoadingPortal(true);
        try {
            const response = await fetch('/api/stripe/create-portal-link', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();
            if (data.url) {
                window.location.href = data.url;
            }
        } catch (error) {
            console.error('Failed to create portal link:', error);
        } finally {
            setIsLoadingPortal(false);
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-2xl mx-auto"
        >
            <h1 className="text-2xl sm:text-3xl font-bold text-center mb-6 text-gray-900 break-words px-4">
                {t('title')}
            </h1>

            <form onSubmit={handleSearch} className="space-y-4 mb-8 sm:mb-12 px-4">
                <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <Input
                        type="email"
                        placeholder={t('emailPlaceholder')}
                        value={email}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
                        className="pl-10 h-12 text-base bg-gray-50 border-gray-200 focus:bg-white"
                        required
                    />
                </div>
                <Button
                    type="submit"
                    className={cn(
                        "w-full h-12 text-base font-medium transition-all",
                        "bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white",
                        "shadow-sm hover:shadow focus:shadow-md",
                        isSearching && "opacity-90 cursor-not-allowed"
                    )}
                    disabled={isSearching}
                >
                    <Search className="mr-2 h-4.5 w-4.5" />
                    {isSearching ? t('searchingButton') : t('searchButton')}
                </Button>
            </form>

            {hasSearched && (
                <div className="space-y-6 px-4">
                    {results.length > 0 ? (
                        <div className="space-y-4">
                            <h2 className="text-xl font-semibold text-gray-900">
                                {t('resultsTitle')}
                            </h2>
                            <div className="space-y-4">
                                {results.filter(r => r.analysis).map((result) => (
                                    <motion.div
                                        key={result.id}
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        className={cn(
                                            "p-3 sm:p-4 rounded-lg border shadow-sm transition-all hover:shadow-md",
                                            result.analysis?.isLocked
                                                ? "border-gray-100 bg-gray-50"
                                                : "border-gray-100 bg-white"
                                        )}
                                    >
                                        <div className="flex flex-col sm:flex-row justify-between gap-4">
                                            <div className="space-y-2 min-w-0">
                                                <div className="flex flex-wrap items-center gap-2">
                                                    <span className={cn(
                                                        "inline-flex items-center px-2.5 py-1 rounded-full text-sm font-medium",
                                                        result.type === 'RAADS'
                                                            ? "bg-blue-50 text-blue-700"
                                                            : "bg-purple-50 text-purple-700"
                                                    )}>
                                                        {result.type} Test
                                                    </span>
                                                    {result.analysis?.isLocked && (
                                                        <Lock className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                                    )}
                                                </div>
                                                <div className="flex flex-wrap items-center gap-x-3 gap-y-2 text-xs sm:text-sm">
                                                    <span className="inline-flex items-center text-gray-600">
                                                        <Calendar className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1.5 flex-shrink-0" />
                                                        {new Date(result.createdAt).toLocaleDateString()}
                                                    </span>
                                                    <span className="inline-flex items-center text-gray-600">
                                                        <FileText className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1.5 flex-shrink-0" />
                                                        ID: {result.id.slice(0, 8)}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center sm:ml-4">
                                                <Button
                                                    variant={result.analysis?.isLocked ? "secondary" : "default"}
                                                    size="sm"
                                                    className={cn(
                                                        "w-full sm:w-auto min-w-[120px] justify-center font-medium",
                                                        result.analysis?.isLocked
                                                            ? "bg-gray-100 text-gray-700 hover:bg-gray-200 border-gray-200"
                                                            : "bg-gray-900 text-white hover:bg-gray-800"
                                                    )}
                                                    onClick={() => handleNavigateToAnalysis(result.id, result.type)}
                                                >
                                                    {t('viewAnalysis')}
                                                </Button>
                                            </div>
                                        </div>
                                        {!result.analysis?.isLocked && result.analysis?.content && (
                                            <div className="mt-4 p-2.5 sm:p-3 bg-gray-50 rounded-md">
                                                <p className="text-xs sm:text-sm text-gray-600 line-clamp-2">
                                                    {result.analysis.content}
                                                </p>
                                            </div>
                                        )}
                                    </motion.div>
                                ))}

                                {results.filter(r => !r.analysis).length > 0 && (
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-medium text-gray-900">
                                            {t('pendingAnalysisTitle')}
                                        </h3>
                                        {results.filter(r => !r.analysis).map((result) => (
                                            <motion.div
                                                key={result.id}
                                                initial={{ opacity: 0, x: -20 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                className="p-3 sm:p-4 rounded-lg border border-blue-100 bg-blue-50/30 hover:bg-blue-50/50 transition-all shadow-sm hover:shadow-md"
                                            >
                                                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                                    <div className="space-y-2 min-w-0">
                                                        <div className="flex items-center gap-2">
                                                            <span className={cn(
                                                                "inline-flex items-center px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-xs sm:text-sm font-medium",
                                                                result.type === 'RAADS'
                                                                    ? "bg-blue-50 text-blue-700"
                                                                    : "bg-purple-50 text-purple-700"
                                                            )}>
                                                                {result.type} Test
                                                            </span>
                                                        </div>
                                                        <div className="flex flex-wrap items-center gap-x-3 gap-y-2 text-xs sm:text-sm">
                                                            <span className="inline-flex items-center text-gray-600">
                                                                <Calendar className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1.5 flex-shrink-0" />
                                                                {new Date(result.createdAt).toLocaleDateString()}
                                                            </span>
                                                            <span className="inline-flex items-center text-gray-600">
                                                                <FileText className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1.5 flex-shrink-0" />
                                                                ID: {result.id.slice(0, 8)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <Button
                                                        variant="default"
                                                        size="sm"
                                                        className="w-full sm:w-auto min-w-[140px] justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                                                        onClick={() => handleNavigateToAnalysis(result.id, result.type)}
                                                    >
                                                        <Sparkles className="h-4 w-4 mr-1.5" />
                                                        {t('generateAnalysis')}
                                                    </Button>
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Add Portal Button */}
                            {results.length > 0 && (
                                <div className="mt-8 text-center">
                                    <button
                                        onClick={handlePortalAccess}
                                        disabled={isLoadingPortal}
                                        className="text-xs text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center"
                                    >
                                        {isLoadingPortal ? (
                                            <Loader2 className="h-3 w-3 animate-spin mr-1.5" />
                                        ) : null}
                                        Manage subscription settings
                                    </button>
                                </div>
                            )}
                        </div>
                    ) : (
                        <div className="text-center space-y-6">
                            <div className="flex items-center justify-center space-x-2 text-amber-600">
                                <AlertCircle className="h-5 w-5" />
                                <p>{t('noResults')}</p>
                            </div>

                            <div className="space-y-4">
                                <h2 className="text-xl font-semibold text-gray-900">
                                    {t('recommendedTests')}
                                </h2>
                                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 px-4 sm:px-0">
                                    <TestCard type={TestType.RAADS} />
                                    <TestCard type={TestType.ADHD} />
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </motion.div>
    )
} 