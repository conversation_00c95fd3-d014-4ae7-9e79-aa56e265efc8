@import url('https://fonts.googleapis.com/css2?family=Chakra+Petch:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --primary-100: #0077C2;
        --primary-200: #59a5f5;
        --primary-300: #c8ffff;
        --accent-100: #00BFFF;
        --accent-200: #00619a;
        --text-100: #333333;
        --text-200: #5c5c5c;
        --bg-100: #FFFFFF;
        --bg-200: #f5f5f5;
        --bg-300: #cccccc;
    }
}

.container {
    @apply max-w-7xl mx-auto px-4 w-full;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 1s ease-in-out;
}

.text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-100 to-accent-100;
}

/* 添加一些实用的颜色类 */
@layer utilities {
    .bg-gradient-soft {
        @apply bg-gradient-to-b from-primary-100/5 to-bg-100;
    }

    .bg-gradient-accent {
        @apply bg-gradient-to-r from-accent-100 to-primary-100;
    }

    .border-gradient {
        @apply border-l-2 border-primary-100/20;
    }
}