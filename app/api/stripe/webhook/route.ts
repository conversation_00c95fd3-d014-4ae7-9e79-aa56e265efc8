import { headers } from 'next/headers';
import { NextResponse } from 'next/server';
import { prisma } from '@/helpers/prisma';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

// 验证项目标识符
const validateProjectIdentifier = (projectIdentifier: string | undefined | null): boolean => {
    if (!projectIdentifier || !process.env.NEXT_PUBLIC_PROJECT_IDENTIFIER) {
        return false;
    }
    return projectIdentifier === process.env.NEXT_PUBLIC_PROJECT_IDENTIFIER;
};

// 处理支付成功后的数据更新
async function handlePaymentSuccess(
    paymentData: {
        stripeId: string;
        amount: number;
        currency: string;
        status: string;
        analysisId: string;
        testId: string;
        mode: 'payment' | 'subscription';
        subscription?: {
            id: string;
            currentPeriodStart: number;
            currentPeriodEnd: number;
        };
    }
) {
    if (!paymentData || !paymentData.analysisId || !paymentData.testId) {
        console.error('Invalid payment data:', paymentData);
        return false;
    }

    try {
        // 1. 验证 analysis 和 test 的关系
        const analysis = await prisma.analysis.findUnique({
            where: {
                id: paymentData.analysisId,
                testId: paymentData.testId
            },
            include: {
                test: true,
                payment: true
            }
        });

        if (!analysis) {
            console.error(`Analysis not found or does not match test: ${paymentData.analysisId}`);
            return false;
        }

        // 2. 检查是否已处理过此支付
        if (analysis.payment?.stripeId === paymentData.stripeId) {
            console.log(`Payment already processed: ${paymentData.stripeId}`);
            return true;
        }

        // 3. 使用事务确保数据一致性
        await prisma.$transaction(async (tx) => {
            // 3.1 更新 analysis 解锁状态
            await tx.analysis.update({
                where: { id: paymentData.analysisId },
                data: { isLocked: false }
            });

            // 3.2 创建支付记录
            const paymentCreateData = {
                stripeId: paymentData.stripeId,
                amount: paymentData.amount,
                currency: paymentData.currency,
                status: paymentData.status,
                analysisId: paymentData.analysisId,
                mode: paymentData.mode,
                ...(paymentData.mode === 'subscription' && paymentData.subscription ? {
                    subscriptionId: paymentData.subscription.id,
                    currentPeriodStart: new Date(paymentData.subscription.currentPeriodStart * 1000),
                    currentPeriodEnd: new Date(paymentData.subscription.currentPeriodEnd * 1000)
                } : {})
            };

            // 记录日志以便调试
            console.log('Creating payment with mode:', paymentData.mode);

            await tx.payment.create({ data: paymentCreateData });
        });

        console.log(`Successfully processed ${paymentData.mode} payment for analysis: ${paymentData.analysisId}`);
        return true;
    } catch (error) {
        console.error('Error handling payment success:', error instanceof Error ? error.message : 'Unknown error');
        return false;
    }
}

// 处理订阅取消
async function handleSubscriptionCancellation(analysisId: string) {
    try {
        await prisma.analysis.update({
            where: { id: analysisId },
            data: { isLocked: true }
        });
        console.log(`Locked analysis after subscription cancellation: ${analysisId}`);
        return true;
    } catch (error) {
        console.error('Error handling subscription cancellation:', error);
        return false;
    }
}

export async function POST(request: Request) {
    try {
        const body = await request.text();
        const headersList = await headers();
        const signature = headersList.get('stripe-signature');

        if (!signature) {
            return new NextResponse('No signature found', { status: 400 });
        }

        let event: Stripe.Event;

        try {
            event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
        } catch (err) {
            console.error('Webhook signature verification failed:', err);
            return new NextResponse('Webhook signature verification failed', { status: 400 });
        }

        console.log(`✅ Processing webhook: ${event.type}`);

        switch (event.type) {
            case 'checkout.session.completed': {
                const session = event.data.object as Stripe.Checkout.Session;
                if (!session) {
                    console.error('Invalid session object');
                    return new NextResponse('Invalid session object', { status: 400 });
                }

                const metadata = session.metadata || {};
                const { testId, analysisId, projectIdentifier } = metadata;

                // 验证项目标识符
                if (!validateProjectIdentifier(projectIdentifier)) {
                    console.error('Invalid project identifier in webhook:', projectIdentifier);
                    return new NextResponse(JSON.stringify({ error: 'Invalid project identifier' }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }

                if (!testId || !analysisId) {
                    console.error('Missing testId or analysisId in session metadata');
                    return new NextResponse('Missing metadata', { status: 400 });
                }

                let subscriptionData;
                if (session.mode === 'subscription' && session.subscription) {
                    const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
                    subscriptionData = {
                        id: subscription.id,
                        currentPeriodStart: subscription.current_period_start,
                        currentPeriodEnd: subscription.current_period_end
                    };
                }

                const success = await handlePaymentSuccess({
                    stripeId: session.id,
                    amount: session.amount_total || 0,
                    currency: session.currency || 'usd',
                    status: session.payment_status || 'completed',
                    mode: session.mode === 'subscription' ? 'subscription' : 'payment',
                    analysisId,
                    testId,
                    subscription: subscriptionData
                });

                if (!success) {
                    return new NextResponse('Failed to process payment', { status: 500 });
                }
                break;
            }

            case 'invoice.paid': {
                const invoice = event.data.object as Stripe.Invoice;
                if (!invoice || !invoice.subscription) {
                    console.error('Invalid invoice object or missing subscription');
                    return new NextResponse('Invalid invoice or no subscription found', { status: 400 });
                }

                // 获取订阅以检查元数据
                const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
                const metadata = subscription.metadata || {};

                // 验证项目标识符
                if (!validateProjectIdentifier(metadata.projectIdentifier)) {
                    console.error('Invalid project identifier in subscription webhook:', metadata.projectIdentifier);
                    return new NextResponse(JSON.stringify({ error: 'Invalid project identifier' }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }

                // 先查找已存在的支付记录
                const existingPayment = await prisma.payment.findFirst({
                    where: {
                        subscriptionId: invoice.subscription as string
                    },
                    include: {
                        analysis: true
                    }
                });

                if (existingPayment) {
                    // 如果存在已有支付记录，使用其关联的信息
                    const success = await handlePaymentSuccess({
                        stripeId: invoice.id,
                        amount: invoice.amount_paid,
                        currency: invoice.currency || 'usd',
                        status: 'paid',
                        mode: 'subscription',
                        analysisId: existingPayment.analysisId,
                        testId: existingPayment.analysis.testId,
                        subscription: {
                            id: invoice.subscription as string,
                            currentPeriodStart: Math.floor(new Date().getTime() / 1000),
                            currentPeriodEnd: Math.floor(new Date().getTime() / 1000) + 30 * 24 * 60 * 60 // 30 days
                        }
                    });

                    if (!success) {
                        return new NextResponse('Failed to process subscription payment', { status: 500 });
                    }
                } else {
                    console.log('No existing payment found for subscription:', invoice.subscription);
                    // 对于首次支付，我们仍然尝试获取 subscription 的 metadata
                    const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
                    const metadata = subscription.metadata || {};
                    const { testId, analysisId } = metadata;

                    if (!testId || !analysisId) {
                        console.error('Missing metadata in subscription:', subscription.id);
                        // 不返回错误，因为这可能是续费支付
                        return new NextResponse('OK', { status: 200 });
                    }

                    const success = await handlePaymentSuccess({
                        stripeId: invoice.id,
                        amount: invoice.amount_paid,
                        currency: invoice.currency || 'usd',
                        status: 'paid',
                        mode: 'subscription',
                        analysisId,
                        testId,
                        subscription: {
                            id: subscription.id,
                            currentPeriodStart: subscription.current_period_start,
                            currentPeriodEnd: subscription.current_period_end
                        }
                    });

                    if (!success) {
                        return new NextResponse('Failed to process subscription payment', { status: 500 });
                    }
                }
                break;
            }

            case 'customer.subscription.deleted':
            case 'customer.subscription.updated': {
                const subscription = event.data.object as Stripe.Subscription;
                if (!subscription) {
                    console.error('Invalid subscription object');
                    return new NextResponse('Invalid subscription object', { status: 400 });
                }

                // Process if deleted or has canceledAt timestamp
                if (event.type === 'customer.subscription.deleted' ||
                    (event.type === 'customer.subscription.updated' && subscription.canceled_at)) {
                    await prisma.payment.updateMany({
                        where: {
                            subscriptionId: subscription.id
                        },
                        data: {
                            canceledAt: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : new Date()
                        }
                    });
                }
                break;
            }
        }

        return new NextResponse('Webhook handled successfully', { status: 200 });
    } catch (error) {
        console.error('Webhook error:', error);
        return new NextResponse('Webhook handler failed', { status: 500 });
    }
}

export const config = {
    api: {
        bodyParser: false,
    },
}; 