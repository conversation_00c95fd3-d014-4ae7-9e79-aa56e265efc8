import Stripe from 'stripe';
import { getStripeCustomerID } from '@/helpers/stripe';
import { NextResponse } from 'next/server';
import { prisma } from '@/helpers/prisma';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

const getBaseUrl = () => {
    if (process.env.NEXT_PUBLIC_SITE_URL) {
        return process.env.NEXT_PUBLIC_SITE_URL.replace(/\/$/, '');
    }
    if (process.env.NEXT_PUBLIC_APP_URL) {
        return process.env.NEXT_PUBLIC_APP_URL.replace(/\/$/, '');
    }
    if (process.env.VERCEL_URL) {
        return `https://${process.env.VERCEL_URL}`;
    }
    return 'http://localhost:3000';
};

export async function POST(request: Request) {
    try {
        const body = await request.json();
        const { testId, priceId, mode, email, testType = 'adhd' } = body;

        // Validate project identifier from the request
        const projectIdentifier = request.headers.get('x-project-identifier');
        if (!projectIdentifier || projectIdentifier !== process.env.NEXT_PUBLIC_PROJECT_IDENTIFIER) {
            return new NextResponse('Invalid project identifier', { status: 403 });
        }

        if (!testId || !priceId || !email) {
            return new NextResponse('Missing required parameters', { status: 400 });
        }

        // Get analysis ID
        const test = await prisma.test.findUnique({
            where: { id: testId },
            include: { analysis: true }
        });

        if (!test) {
            return new NextResponse('Test not found', { status: 404 });
        }

        if (!test.analysis) {
            return new NextResponse('Analysis not found', { status: 404 });
        }

        const baseUrl = getBaseUrl();
        const testPath = testType === 'adhd' ? 'adhd-test' : 'raads-r-test';

        // Get or create Stripe customer
        const customerId = await getStripeCustomerID({
            email,
            user_id: email,
        });

        // Create Stripe checkout session
        const stripeSession = await stripe.checkout.sessions.create({
            customer: customerId,
            mode: mode === 'subscription' ? 'subscription' : 'payment',
            payment_method_types: ['card'],
            line_items: [{
                price: priceId,
                quantity: 1,
            }],
            success_url: `${baseUrl}/${testPath}/${testId}/analysis?success=true`,
            cancel_url: `${baseUrl}/${testPath}/${testId}/analysis?canceled=true`,
            metadata: {
                testId,
                analysisId: test.analysis.id,
                testType,
                projectIdentifier: process.env.NEXT_PUBLIC_PROJECT_IDENTIFIER,
            },
            subscription_data: mode === 'subscription' ? {
                metadata: {
                    testId,
                    analysisId: test.analysis.id,
                    testType,
                    projectIdentifier: process.env.NEXT_PUBLIC_PROJECT_IDENTIFIER,
                }
            } : undefined,
        });

        return NextResponse.json({ url: stripeSession.url });
    } catch (error) {
        console.error('Stripe session creation error:', error);
        return new NextResponse('Internal error', { status: 500 });
    }
}