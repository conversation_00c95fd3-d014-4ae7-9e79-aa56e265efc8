import { NextResponse } from 'next/server';
import <PERSON>e from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

const getBaseUrl = () => {
    if (process.env.NEXT_PUBLIC_SITE_URL) {
        return process.env.NEXT_PUBLIC_SITE_URL.replace(/\/$/, '');
    }
    if (process.env.NEXT_PUBLIC_APP_URL) {
        return process.env.NEXT_PUBLIC_APP_URL.replace(/\/$/, '');
    }
    if (process.env.VERCEL_URL) {
        return `https://${process.env.VERCEL_URL}`;
    }
    return 'http://localhost:3000';
};

export async function POST(request: Request) {
    try {
        const { email } = await request.json();

        if (!email) {
            return NextResponse.json({ error: "Email is required" }, { status: 400 });
        }

        // Search for customer by email
        const customers = await stripe.customers.list({
            email: email,
            limit: 1,
        });

        if (!customers.data.length) {
            return NextResponse.json({ error: "No associated Stripe customer" }, { status: 400 });
        }

        const baseUrl = getBaseUrl();
        const portalSession = await stripe.billingPortal.sessions.create({
            customer: customers.data[0].id,
            return_url: `${baseUrl}/analysis`,
        });

        return NextResponse.json({ url: portalSession.url });
    } catch (error) {
        console.error('Error creating portal link:', error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}