import { prisma } from '@/helpers/prisma'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')

        if (!id) {
            return NextResponse.json(
                { error: 'Test ID is required' },
                { status: 400 }
            )
        }

        const test = await prisma.test.findUnique({
            where: {
                id: id
            },
            include: {
                analysis: true
            }
        })

        if (!test) {
            return NextResponse.json(
                { error: 'Test not found' },
                { status: 404 }
            )
        }

        return NextResponse.json(test)
    } catch (error) {
        console.error('Error fetching test:', error)
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        )
    }
}

export async function POST(request: Request) {
    try {
        const body = await request.json()
        const { type, email, scores, answers } = body

        const test = await prisma.test.create({
            data: {
                type,
                email,
                scores,
                answers
            }
        })

        return NextResponse.json({ test })
    } catch (error) {
        console.error('Failed to save test:', error)
        return NextResponse.json(
            { error: 'Failed to save test' },
            { status: 500 }
        )
    }
} 