import { NextRequest } from 'next/server';
import { AIServiceManager } from '@/helpers/aiServiceManager';

export async function POST(request: NextRequest) {
    const body = await request.json();
    const { prompt } = body;

    if (!prompt) {
        return new Response(JSON.stringify({ error: 'Prompt is required' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
        });
    }

    const encoder = new TextEncoder();
    const aiService = new AIServiceManager();

    try {
        const stream = new TransformStream();
        const writer = stream.writable.getWriter();

        // 使用新的 AI 服务管理器，支持自动降级
        (async () => {
            try {
                for await (const content of aiService.generateStreamWithFallback(prompt)) {
                    await writer.write(
                        encoder.encode(`data: ${JSON.stringify({
                            content: content
                        })}\n\n`)
                    );
                }
                await writer.write(encoder.encode('data: [DONE]\n\n'));
                await writer.close();
            } catch (error) {
                console.error('AI generation error:', error);
                await writer.write(
                    encoder.encode(`data: ${JSON.stringify({
                        error: "AI_GENERATION_ERROR",
                        message: (error as Error).message
                    })}\n\n`)
                );
                await writer.close();
            }
        })();

        return new Response(stream.readable, {
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
            },
        });

    } catch (error) {
        console.error('API error:', error);
        return new Response(
            JSON.stringify({
                error: "API_ERROR",
                message: (error as Error).message
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}