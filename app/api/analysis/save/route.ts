import { prisma } from '@/helpers/prisma';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
    try {
        const body = await request.json();
        const { content, testId, scores, answers, email } = body;

        if (!testId) {
            return NextResponse.json(
                { error: 'Test ID is required' },
                { status: 400 }
            );
        }

        // 检查测试记录是否存在
        const existingTest = await prisma.test.findUnique({
            where: { id: testId },
            include: { analysis: true }
        });

        if (!existingTest) {
            // 如果测试记录不存在但有完整数据，创建新记录
            if (scores && answers && email) {
                const newTest = await prisma.test.create({
                    data: {
                        id: testId,
                        type: 'RAADS',
                        email,
                        scores,
                        answers,
                    },
                });

                const analysis = await prisma.analysis.create({
                    data: {
                        content,
                        isLocked: true,
                        testId: newTest.id,
                    },
                });

                return NextResponse.json({
                    test: newTest,
                    analysis
                });
            }

            return NextResponse.json(
                { error: 'Test not found' },
                { status: 404 }
            );
        }

        // 更新现有记录
        if (scores && answers && email) {
            await prisma.test.update({
                where: { id: testId },
                data: { scores, answers, email },
            });
        }

        // 处理分析内容
        if (existingTest.analysis) {
            const updatedAnalysis = await prisma.analysis.update({
                where: { id: existingTest.analysis.id },
                data: {
                    content,
                    isLocked: true,
                },
            });

            return NextResponse.json({
                test: existingTest,
                analysis: updatedAnalysis
            });
        }

        const newAnalysis = await prisma.analysis.create({
            data: {
                content,
                isLocked: true,
                testId: existingTest.id,
            },
        });

        return NextResponse.json({
            test: existingTest,
            analysis: newAnalysis
        });

    } catch (error) {
        console.error('Failed to save analysis:', error);
        return NextResponse.json(
            { error: 'Failed to save analysis' },
            { status: 500 }
        );
    }
} 