import { prisma } from '@/helpers/prisma'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
        return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    try {
        const tests = await prisma.test.findMany({
            where: {
                email: email,
            },
            include: {
                analysis: {
                    include: {
                        payment: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        })

        return NextResponse.json(tests)
    } catch (error) {
        console.error('Search failed:', error)
        return NextResponse.json({ error: 'Search failed' }, { status: 500 })
    }
} 