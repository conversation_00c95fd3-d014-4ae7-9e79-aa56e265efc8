{"name": "autism_test", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_ENV=development prisma generate && next dev", "build": "NODE_ENV=production prisma generate && next build", "start": "NODE_ENV=production next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:push": "prisma db push", "prisma:studio": "prisma studio"}, "dependencies": {"@auth/core": "^0.34.2", "@auth/prisma-adapter": "^2.4.2", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@google-cloud/vertexai": "^1.9.0", "@headlessui/react": "^2.1.3", "@heroicons/react": "^1.0.6", "@prisma/client": "^5.17.0", "@radix-ui/react-slot": "^1.1.0", "@stripe/stripe-js": "^4.10.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.7.7", "chart.js": "^4.4.6", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "google-auth-library": "^9.15.0", "lucide-react": "^0.460.0", "next": "^15.0.3", "next-auth": "^4.24.7", "next-intl": "^3.25.0", "next-themes": "^0.3.0", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-masonry-css": "^1.0.16", "react-share": "^5.1.0", "stripe": "^16.8.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "prisma": "^5.17.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}