#!/bin/bash

# 拉取最新的 Docker 镜像
echo "正在拉取最新的 Docker 镜像..."
if docker-compose -f docker-compose.prod.yml pull autism_test; then
    echo "Docker 镜像拉取成功。"
else
    echo "Docker 镜像拉取失败！"
    exit 1
fi

# 停止 Docker 容器
echo "正在停止 Docker 容器..."
if docker-compose -f docker-compose.prod.yml stop autism_test; then
    echo "Docker 容器停止成功。"
else
    echo "Docker 容器停止失败！"
    exit 1
fi

# 启动 Docker 容器
echo "正在启动 Docker 容器..."
if docker-compose -f docker-compose.prod.yml up autism_test -d; then
    echo "Docker 容器启动成功。"
else
    echo "Docker 容器启动失败！"
    exit 1
fi

echo "所有操作成功完成。"
